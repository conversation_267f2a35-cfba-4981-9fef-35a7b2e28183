#!/bin/bash

# 定义颜色
GREEN="\033[32m"
RED="\033[31m"
YELLOW="\033[33m"
BLUE="\033[36m"
RESET="\033[0m"

# 定义要停止的 JAR 文件名称
JAR_NAMES=("modify-service-1.0.jar")

echo -e "${BLUE}======== 🚀 JAR 服务停止脚本启动 ========${RESET}"

# 遍历每个 JAR 名称
for JAR_NAME in "${JAR_NAMES[@]}"; do
    echo -e "\n${YELLOW}➤ 正在处理：${JAR_NAME}${RESET}"

    # 查找对应的 PID 列表
    PIDS=$(ps aux | grep "$JAR_NAME" | grep -v grep | awk '{print $2}')

    if [ -z "$PIDS" ]; then
        echo -e "${RED}✘ 未发现正在运行的进程${RESET}"
    else
        for PID in $PIDS; do
            # 显示 PID 并尝试 kill
            echo -ne "  ➤ 停止进程 PID ${PID} ... "
            kill -9 "$PID" 2>/dev/null
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✔︎ 已终止${RESET}"
            else
                echo -e "${RED}✘ 停止失败${RESET}"
            fi
        done
    fi
done

echo -e "\n${BLUE}======== ✅ 所有处理完成 ========${RESET}"