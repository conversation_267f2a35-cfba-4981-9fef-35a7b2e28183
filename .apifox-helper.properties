# æ­¤æä»¶ç¨æ¥éç½®ApifoxçIdeaæä»¶çææ¥å£ææ¡£æ¶çä¸äºè§åéç½®



json.rule.enum.convert[groovy:it.isExtend("com.tjsj.common.enums.DescriptionAble")]=~#description

# è·åfolderNameæä»¶å¤¹åç§°
folder.name=@io.swagger.v3.oas.annotations.tags.Tag#description
folder.name=#description

# å¿½ç¥ç±»ææ¥å£
ignore=#ignore

# å­æ®µè®¾ç½®é»è®¤nullå¼
field.schema.permit.null=true
#field.schema.permit.null[groovy=!it.hasAnn('javax.validation.constraints.NotNull')]=true

# å­æ®µè®¾ç½®å¿é
field.required=true
field.schema.format=@io.swagger.v3.oas.annotations.media.Schema#type

# è½¬æ¢ç±»å
json.rule.convert[com.tjsj.common.enums.base.CommonStatus]=java.lang.Integer
#json.rule.enum.convert[com.tjsj.common.enums.base.CommonStatus]=~#code
json.rule.enum.convert[groovy:it.isExtend("com.tjsj.common.enums.BaseEnum")]=~#code

#æ¥å£æè¿°
method.description=@io.swagger.v3.oas.annotations.Operation#description
method.description[#deprecated]=groovy:"\nãå·²åºå¼ã" + it.doc("deprecated")
method.description[@java.lang.Deprecated]=ãå·²åºå¼ã

#method.description[groovy:it.containingClass().hasDoc("deprecated")]=groovy:"\nãå·²åºå¼ã" + it.containingClass().doc
# ("deprecated")
method.description[groovy:it.containingClass().hasAnn("java.lang.Deprecated")]=ãå·²åºå¼ã