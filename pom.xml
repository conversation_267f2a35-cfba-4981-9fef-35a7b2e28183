<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.18</version>
		<relativePath/>
	</parent>
	<packaging>pom</packaging>
	<groupId>com.tjsj</groupId>
	<artifactId>backend</artifactId>
	<version>1.0</version>
	<name>backend</name>
	<description>backend for Spring Boot</description>
	<properties>
		<java.version>17</java.version>
		<maven.compiler.source>${java.version}</maven.compiler.source>
		<maven.compiler.target>${java.version}</maven.compiler.target>
		<maven.compiler.release>${java.version}</maven.compiler.release>
	</properties>

	<modules>
		<module>common-pom</module>
		<module>modify-service</module>
	</modules>

	<!-- 如下配置，如果是微服务项目，可以只加在最外层的pom.xml中，子模块只需加上面的配置即可  -->
	<!--用于指定本项目的jar包的发布位置-->
	<distributionManagement>
		<snapshotRepository>
			<!-- 这里的id与settings.xml中配置的id保持一致  -->
			<id>local-snapshot</id>
			<name>LOCAL-SNAPSHOT</name>
			<url>http://tarkindata.com:8071/repository/maven-snapshots/</url>
		</snapshotRepository>

		<repository>
			<!-- 这里的id与settings.xml中配置的id保持一致  -->
			<id>local-release</id>
			<name>LOCAL-RELEASE</name>
			<url>http://tarkindata.com:8071/repository/maven-releases/</url>
		</repository>
	</distributionManagement>
</project>
