# ████████████████████████████████████████████████████████████████████████████████
# ████████                    生产环境配置文件                        ████████
# ████████                application-prod.yml                      ████████
# ████████████████████████████████████████████████████████████████████████████████
#
# 【文件作用】
# 本文件为 modify_database_data_service 项目的生产环境配置文件
# 包含数据库连接、Redis缓存、日志级别等核心配置
#
# 【维护说明】
# 🔴【必改】- 需要根据实际环境修改的配置（数据库地址、密码等）
# 🟡【可选】- 可根据需要调整的配置（连接池大小、超时时间等）
# 🟢【默认】- 通常不需要修改的配置（日志级别、基础设置等）
#
#
# ████████████████████████████████████████████████████████████████████████████████

# ⚠️ ⚠️ ⚠️ 必改配置项快速定位索引 ⚠️ ⚠️ ⚠️
# ┌─────────────────────────────────────────────────────────────────────────────┐
# │ 🔴 数据库连接地址 - spring.datasource.dynamic.datasource.tj_middle_ground.url │
# │ 🔴 数据库用户名   - spring.datasource.dynamic.datasource.tj_middle_ground.username │
# │ 🔴 数据库密码     - spring.datasource.dynamic.datasource.tj_middle_ground.password │
# │ 🔴 Redis服务器   - spring.redis.host                                        │
# │ 🔴 Redis端口     - spring.redis.port                                        │
# │ 🔴 Redis密码     - spring.redis.password                                    │
# └─────────────────────────────────────────────────────────────────────────────┘

# ████████████████████████████████████████████████████████████████████████████████
# ████████                      数据库配置区域                        ████████
# ████████████████████████████████████████████████████████████████████████████████

spring:
  datasource:
    dynamic:
      primary: tj_middle_ground  # 🟢 主数据源标识，通常不需要修改
      datasource:
        # ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
        # ┃                          【必改】数据源配置                                ┃
        # ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
        tj_middle_ground: # 数据源tj_middle_ground
          #  🔴 请修改为实际的数据库连接信息
          url: ***********************************************************************************************************************************************************************************************************************************************************************************************  # 🔴 请修改为实际的数据库连接地址
          username: xyyw@xyyw#bhtsobb02  # 🔴 请修改为实际的数据库用户名
          password: XYyw_2024!  # 🔴 请修改为实际的数据库密码
          druid:
            validation-query: SELECT 1 FROM DUAL  # 🟢 数据库连接验证查询，通常不需要修改

    # ┌─────────────────────────────────────────────────────────────────────────────┐
    # │                            【可选】连接池配置                                 │
    # └─────────────────────────────────────────────────────────────────────────────┘
    druid:
      initial-size: 50  # 🟡 初始连接数，可根据并发量调整
      max-active: 6000  # 🟡 最大连接数，可根据服务器性能调整
      min-idle: 10  # 🟡 最小空闲连接数，建议保持默认值
      max-wait: 600000  # 🟡 获取连接最大等待时间(ms)，可根据需要调整
      pool-prepared-statements: true  # 🟢 开启预编译语句池，建议保持开启
      max-pool-prepared-statement-per-connection-size: 20  # 🟡 每个连接的预编译语句池大小
      time-between-eviction-runs-millis: 60000  # 🟡 连接回收检测间隔(ms)
      min-evictable-idle-time-millis: 300000  # 🟡 连接最小空闲时间(ms)
      validation-query: SELECT 1 FROM DUAL  # 🟢 连接验证查询
      test-while-idle: true  # 🟢 空闲时检测连接有效性
      test-on-borrow: false  # 🟢 获取连接时不检测
      test-on-return: false  # 🟢 归还连接时不检测
      stat-view-servlet:
        enabled: true  # 🟡 是否开启Druid监控页面
        url-pattern: /druid/*  # 🟡 监控页面访问路径
      filter:
        wall:
          enabled: true  # 🟢 开启SQL防火墙
          config:
            multi-statement-allow: true  # 🟢 允许多语句执行
        stat:
          log-slow-sql: true  # 🟡 是否记录慢SQL
          slow-sql-millis: 10  # 🟡 慢SQL阈值(ms)，可根据需要调整
          merge-sql: false  # 🟢 是否合并SQL统计

  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure  # 🟢 排除Druid自动配置

# ████████████████████████████████████████████████████████████████████████████████
# ████████                      Redis缓存配置区域                     ████████
# ████████████████████████████████████████████████████████████████████████████████

  # ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
  # ┃                          【必改】Redis连接配置                              ┃
  # ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
  redis:
    database: 0  # 🟡 Redis数据库索引，可根据需要调整
    host: ************  # 🔴 请修改为实际的Redis服务器地址
    port: 8886  # 🔴 请修改为实际的Redis端口
    password: xyzq1qaz2wsx  # 🔴 请修改为实际的Redis密码
    timeout: 10000ms  # 🟡 连接超时时长，可根据网络情况调整
    jedis:
      pool:
        max-active: 5  # 🟡 连接池最大连接数，可根据并发量调整
        max-idle: 25  # 🟡 连接池最大空闲连接数
        min-idle: 10  # 🟡 连接池最小空闲连接数

# ████████████████████████████████████████████████████████████████████████████████
# ████████                    项目自定义配置区域                      ████████
# ████████████████████████████████████████████████████████████████████████████████

# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                          环境标识配置                                ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
tarkin:
  env: xyzq
  save-log: false # 是否保存日志到数据库，默认true

# ████████████████████████████████████████████████████████████████████████████████
# ████████                      日志配置区域                          ████████
# ████████████████████████████████████████████████████████████████████████████████

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                            【默认】日志级别配置                               │
# └─────────────────────────────────────────────────────────────────────────────┘
logging:
  level:
    root: INFO  # 🟢 根日志级别，生产环境建议使用WARN级别


# ████████████████████████████████████████████████████████████████████████████████
# ████████                      维护提醒区域                          ████████
# ████████████████████████████████████████████████████████████████████████████████
#
# 【配置修改注意事项】
# 1. 修改配置前请务必备份原文件
# 2. 数据库和Redis连接信息必须与实际环境匹配
# 3. 连接池参数调整需要根据服务器性能和并发量评估
# 4. 修改完成后请更新文件头部的"最后更新"时间
#
# ████████████████████████████████████████████████████████████████████████████████
