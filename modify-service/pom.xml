<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tjsj</groupId>
        <artifactId>common-pom</artifactId>
        <version>1.0</version>
        <relativePath>../common-pom/pom.xml</relativePath>
    </parent>

    <artifactId>modify-service</artifactId>
    <version>1.0</version>
    <name>modify-service</name>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.tjsj</groupId>
            <artifactId>common-code</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>*</artifactId> <!-- * 表示排除所有该 groupId 下的依赖 -->
                </exclusion>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <!--排除xxl-job相关依赖-->
                <exclusion>
                    <groupId>com.xuxueli</groupId>
                    <artifactId>*</artifactId>
                </exclusion>

            </exclusions>

        </dependency>


        <dependency>
            <groupId>org.openjdk.nashorn</groupId>
            <artifactId>nashorn-core</artifactId>
            <version>15.3</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!--关闭 Polyglot 引擎相关的警告提示-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>-Dpolyglot.engine.WarnInterpreterOnly=false</argLine>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>