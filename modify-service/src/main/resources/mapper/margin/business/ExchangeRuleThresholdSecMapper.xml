<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.margin.business.mapper.ExchangeRuleThresholdSecMapper">


  <select id="selectStockHaircutUpperLimit" resultType="ExchangeRuleThresholdSecDO">
      SELECT si.sec_code,
             IFNULL(cs.threshold_value,
                    (SELECT tc.threshold_value
                     FROM margin.t_exchange_rule_threshold_config tc
                     WHERE tc.type_name = '其余股票'
                       and tc.sec_type = 0
                     LIMIT 1)
             ) AS haircut_upper_limit,
             IFNULL(cs.threshold_value,
                    (SELECT tc.threshold_value
                     FROM margin.t_exchange_rule_threshold_config tc
                     WHERE tc.type_name = '其余股票'
                       and tc.sec_type = 0
                     LIMIT 1)
             ) AS haircut_upper_limit_without_static_pe
      FROM credit.t_rzrq_sec_info si
               LEFT JOIN (SELECT cs.code, tc.threshold_value
                          FROM credit.t_dfcf_component_stock cs
                                   INNER JOIN margin.t_exchange_rule_threshold_config tc
                                              ON cs.block_name = tc.type_name
                          WHERE tc.mrd_trd_type = 'collateral') cs ON si.code = cs.code
      WHERE si.sec_type = 0
      ORDER BY si.sec_code;
  </select>

  <select id="selectOtherSecHaircutUpperLimit"
          resultType="ExchangeRuleThresholdSecDO">

      SELECT si.sec_code,
             IFNULL(tc.threshold_value,
                    (SELECT tc.threshold_value
                     FROM margin.t_exchange_rule_threshold_config tc
                     WHERE tc.type_name = '其他基金'
                       and tc.sec_type = 1
                     LIMIT 1)
             ) AS haircut_upper_limit
      from credit.t_rzrq_sec_info si
               LEFT JOIN
           margin.t_exchange_rule_threshold_config tc
           on si.sec_category = tc.type_name and tc.sec_type = 1
      WHERE si.sec_type = 1

      UNION

      SELECT si.sec_code,
             IFNULL(tc.threshold_value,
                    (SELECT tc.threshold_value
                     FROM margin.t_exchange_rule_threshold_config tc
                     WHERE tc.type_name = '其他债券'
                       and tc.sec_type = 2
                     LIMIT 1)
             ) AS haircut_upper_limit
      from credit.t_rzrq_sec_info si
               LEFT JOIN
           margin.t_exchange_rule_threshold_config tc
           on si.sec_category = tc.type_name and tc.sec_type = 2
      WHERE si.sec_type = 2
  </select>
</mapper>