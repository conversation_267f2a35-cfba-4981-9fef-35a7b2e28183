<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.market.mapper.MarketUpdateUtilMapper">

    <select id="selectNewExchangeStaticPeSecData" resultType="ExchangeStaticPeSecDO">
        SELECT si.sec_code, si.stock_name as sec_name, h.PES as static_pe, h.Date
        from (SELECT h.StockId, max(date) as date
              from pledgedata.t_stockhistory_0827 h
              WHERE h.date <![CDATA[ <= ]]>
        (SELECT MAX(date) AS date
                     from (SELECT date
                           from pledgedata.t_stockhistory_0827
                           WHERE date > '2024-01-01'
                           GROUP BY date) date
                   <if test="ifTodayLastTradingDayThisWeekAndMarketDataUpdated == true">
                       WHERE YEARWEEK(date, 1) <![CDATA[ <= ]]> YEARWEEK(CURDATE(), 1)
                   </if>
                   <if test="ifTodayLastTradingDayThisWeekAndMarketDataUpdated == false">
                       WHERE YEARWEEK(date, 1) <![CDATA[ < ]]> YEARWEEK(CURDATE(), 1)
                   </if>
        )
              GROUP BY h.StockId) as hd
                 INNER JOIN
             pledgedata.t_stockhistory_0827 h
             on hd.date = h.Date and hd.StockId = h.StockId
                 INNER JOIN
             tj_middle_ground.t_stock_info si
             on h.stockId = si.stock_id and hd.StockId = si.stock_id
        WHERE si.status = 0 and (h.PES <![CDATA[ <= ]]>  0 or h.PES > 300);
    </select>

    <select id="selectNewStockTradingDay" resultType="SecTradingDayDO">
        SELECT StockId AS sec_code,
               date
        FROM pledgedata.t_stockhistory_0827
        WHERE DATE >= DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH)
          AND DATE <![CDATA[ <= ]]> CURRENT_DATE
          AND `Close` IS NOT NULL;
    </select>

    <select id="selectMinTradingDay" resultType="com.tjsj.modify.modules.market.model.entity.SecTradingDayDO">
        SELECT h.sec_code, h.date, h.trading_number
        from (SELECT StockId,
                     min(date) as date
              FROM pledgedata.t_stockhistory_0827
              WHERE DATE >= DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH)
                AND DATE <![CDATA[ <= ]]> CURRENT_DATE
                AND `Close` IS NOT NULL
              GROUP BY StockId) hmd
                 INNER JOIN
             pledgedata.t_sec_trading_day h
             on hmd.stockId = h.sec_code and hmd.date = h.date;
    </select>

    <select id="selectOldStockTradingDay" resultType="SecTradingDayDO">
        SELECT
            id,sec_code,date,trading_number
        FROM pledgedata.t_sec_trading_day
        WHERE
            DATE >= DATE_SUB( CURRENT_DATE, INTERVAL 1 MONTH )
          AND DATE <![CDATA[ <= ]]> CURRENT_DATE;
    </select>

    <select id="selectBeforeMonthSecMaxTradingDate" resultType="SecTradingDayDO">
        SELECT d.sec_code,
               d.date,
               d.trading_number
        FROM (SELECT d.sec_code,
                     max(d.DATE) AS DATE
              FROM pledgedata.t_sec_trading_day d
              WHERE d.DATE <![CDATA[ <= ]]> DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH)
              GROUP BY d.sec_code) md
                 INNER JOIN pledgedata.t_sec_trading_day d ON md.sec_code = d.sec_code
            AND md.DATE = d.DATE
    </select>

    <select id="checkIfLastTradingDayThisWeekAndMarketDataUpdated" resultType="java.lang.Integer">
        SELECT count(0) AS count
        FROM (SELECT DATE
              FROM pledgedata.t_tradingdays
              WHERE DATE <![CDATA[ <= ]]> (SELECT DATE_ADD(
                                            CURDATE(),
                                            INTERVAL (
                                                7 - (IF(DAYOFWEEK(CURDATE()) = 1, 7, DAYOFWEEK(CURDATE()) - 1))) DAY
                                    ))
              ORDER BY DATE DESC
              LIMIT 1) d
                 INNER JOIN pledgedata.t_stockhistory_0827 h ON h.DATE = d.DATE
    </select>
</mapper>