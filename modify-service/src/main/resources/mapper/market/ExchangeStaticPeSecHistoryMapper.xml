<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.market.mapper.ExchangeStaticPeSecHistoryMapper">
  <resultMap id="BaseResultMap" type="com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecHistoryDO">
    <!--@mbg.generated-->
    <!--@Table t_exchange_static_pe_sec_history-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sec_code" jdbcType="VARCHAR" property="secCode" />
    <result column="sec_name" jdbcType="VARCHAR" property="secName" />
    <result column="static_pe" jdbcType="DECIMAL" property="staticPe" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sec_code, sec_name, static_pe, `date`, create_time, update_time
  </sql>

  <select id="getHistoryDate" resultType="java.lang.String">
      SELECT MAX(date)         AS last_trading_day,
             YEARWEEK(date, 1) AS year_week
      FROM pledgedata.t_stockhistory_0827
      WHERE date >= '2025-01-01'
        and YEARWEEK(date, 1)  <![CDATA[ < ]]> YEARWEEK(CURDATE(), 1)
      GROUP BY YEARWEEK(date, 1)
      ORDER BY year_week desc;
  </select>

  <insert id="insertExchangeStaticPeDateData">
      insert ignore into tj_middle_ground.t_exchange_static_pe_sec_history
          (sec_code, sec_name, static_pe, `date`)

      SELECT si.sec_code, si.stock_name as sec_name, h.PES as static_pe, #{date,jdbcType=VARCHAR}
      from (SELECT h.StockId, max(date) as date
            from pledgedata.t_stockhistory_0827 h
            WHERE h.date <![CDATA[ <= ]]>
    (SELECT MAX(date) AS date
                   from (SELECT date
                         from pledgedata.t_stockhistory_0827
                         WHERE date >= '2025-01-01'
                         GROUP BY date) date
                   WHERE YEARWEEK(date, 1) <![CDATA[ <= ]]> YEARWEEK(#{date,jdbcType=VARCHAR}, 1))
            GROUP BY h.StockId) as hd
               INNER JOIN
           pledgedata.t_stockhistory_0827 h
           on hd.date = h.Date and hd.StockId = h.StockId
               INNER JOIN
           tj_middle_ground.t_stock_info si
           on h.stockId = si.stock_id and hd.StockId = si.stock_id
      WHERE (h.PES <![CDATA[ <= ]]>  0 or h.PES > 300);
  </insert>


</mapper>