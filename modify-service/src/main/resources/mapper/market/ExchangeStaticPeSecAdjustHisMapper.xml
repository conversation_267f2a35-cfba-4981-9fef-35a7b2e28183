<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.market.mapper.ExchangeStaticPeSecAdjustHisMapper">

    <select id="getHistoryDateList" resultType="java.lang.String">
        SELECT MAX(date)         AS last_trading_day,
               YEARWEEK(date, 1) AS year_week
        FROM pledgedata.t_stockhistory_0827
        WHERE date >= '2024-01-01'
          and YEARWEEK(date, 1)  <![CDATA[ < ]]> YEARWEEK(CURDATE(), 1)
        GROUP BY YEARWEEK(date, 1)
        ORDER BY year_week desc;
    </select>

    <select id="getStockStaticPeByDate"
            resultType="ExchangeStaticPeSecAdjustHisDO">
        SELECT h.StockId as sec_code, h.PES as static_pe, #{date,jdbcType=VARCHAR} as date
        from (SELECT h.StockId, max(date) as date
              from pledgedata.t_stockhistory_0827 h
              WHERE h.Date >= DATE_SUB(#{date,jdbcType=VARCHAR}, INTERVAL 1 MONTH)
                and h.Date <![CDATA[ <= ]]> #{date,jdbcType=VARCHAR}
              GROUP BY h.StockId) md
                 INNER JOIN
             pledgedata.t_stockhistory_0827 h
             on md.stockId = h.stockId and md.date = h.date;
    </select>

    <insert id="insertStaticPeSecAdjustHis">
        insert ignore into tj_middle_ground.t_exchange_static_pe_sec_adjust_his (sec_code, sec_name, adjust_type, static_pe,
                                                                          last_week_last_trading_day_static_pe, date)


        SELECT si.sec_code,
               si.stock_name,
               h.adjust_type,
               h.new_static_pe               as staticPe,
               h.old_static_pe               as lastWeekLastTradingDayStaticPe,
               #{afterDate,jdbcType=VARCHAR} as date
        from (SELECT nh.StockId,
                     oh.PES                                                                               as old_static_pe,
                     nh.PES                                                                               as new_static_pe,
                     if((nh.PES > 300 or nh.PES <![CDATA[ < ]]> 0) and (oh.PES <![CDATA[ <= ]]> 300 and oh.PES >= 0), '下调', '恢复') as adjust_type
              from (SELECT h.StockId, h.PES
                    from (SELECT h.StockId, max(date) as date
                          from pledgedata.t_stockhistory_0827 h
                          WHERE h.Date >= DATE_SUB(#{afterDate,jdbcType=VARCHAR}, INTERVAL 6 MONTH)
                            and h.Date <![CDATA[ <= ]]> #{afterDate,jdbcType=VARCHAR}
                          GROUP BY h.StockId) md
                             INNER JOIN
                         pledgedata.t_stockhistory_0827 h
                         on md.stockId = h.stockId and md.date = h.date) nh
                       INNER JOIN
                   (SELECT h.StockId, h.PES
                    from (SELECT h.StockId, max(date) as date
                          from pledgedata.t_stockhistory_0827 h
                          WHERE h.Date >= DATE_SUB(#{beforeDate,jdbcType=VARCHAR}, INTERVAL 6 MONTH)
                            and h.Date <![CDATA[ <= ]]> #{beforeDate,jdbcType=VARCHAR}
                          GROUP BY h.StockId) md
                             INNER JOIN
                         pledgedata.t_stockhistory_0827 h
                         on md.stockId = h.stockId and md.date = h.date) oh
                   on nh.StockId = oh.StockId
              WHERE ((nh.PES > 300 or nh.PES <![CDATA[ < ]]> 0) and (oh.PES <![CDATA[ <= ]]> 300 and oh.PES >= 0))
                 or ((oh.PES > 300 or oh.PES <![CDATA[ < ]]> 0) and (nh.PES <![CDATA[ <= ]]> 300 and nh.PES >= 0))) h
                 INNER JOIN
             tj_middle_ground.t_stock_info si
             on h.StockId = si.stock_id
        ORDER BY h.StockId;
    </insert>
</mapper>