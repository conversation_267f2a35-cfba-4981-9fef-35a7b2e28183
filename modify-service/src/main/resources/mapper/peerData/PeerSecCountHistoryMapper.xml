<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.peer.mapper.PeerSecCountHistoryMapper">
    <resultMap id="BaseResultMap" type="com.tjsj.modify.modules.peer.model.entity.PeerSecCountHistoryDO">
        <!--@mbg.generated-->
        <!--@Table margin.t_peer_sec_count_history-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="collateral_count" jdbcType="INTEGER" property="collateralCount"/>
        <result column="finance_target_count" jdbcType="INTEGER" property="financeTargetCount"/>
        <result column="short_sell_target_count" jdbcType="INTEGER" property="shortSellTargetCount"/>
        <result column="concentra_group_count" jdbcType="INTEGER" property="concentraGroupCount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        `source`,
        `date`,
        collateral_count,
        finance_target_count,
        short_sell_target_count,
        concentra_group_count,
        create_time,
        update_time
    </sql>

    <select id="selectNewCollateralCountRecord" resultType="PeerSecCountHistoryDO">
        SELECT d.DATE,
               count(0) as collateral_count
        FROM margin.t_peer_sec_collateral_data d
        <where>
            d.${peerEnName} IS NOT NULL
              AND d.${peerEnName} NOT LIKE '%受限%'
            <if test="startDate != null">
                and  d.DATE >= #{startDate,jdbcType=DATE}
            </if>
        </where>

        GROUP BY d.DATE
        ORDER BY d.DATE DESC;
    </select>

    <select id="selectNewFinancingTargetCountRecord" resultType="PeerSecCountHistoryDO">
        SELECT d.DATE,
               count(0) as finance_target_count
        FROM margin.t_peer_sec_finance_target_data d
        <where>
            d.${peerEnName} IS NOT NULL
            <if test="startDate != null">
                and  d.DATE >= #{startDate,jdbcType=DATE}
            </if>
        </where>
        GROUP BY d.DATE
        ORDER BY d.DATE DESC;
    </select>

    <select id="selectNewShortSellTargetCountRecord" resultType="PeerSecCountHistoryDO">
        SELECT d.DATE,
               count(0) as short_sell_target_count
        FROM margin.t_peer_sec_short_sell_target_data d
        <where>
            d.${peerEnName} IS NOT NULL
            <if test="startDate != null">
                and  d.DATE >= #{startDate,jdbcType=DATE}
            </if>
        </where>
        GROUP BY d.DATE
        ORDER BY d.DATE DESC;

    </select>

    <select id="selectNewConcentraGroupCountRecord" resultType="PeerSecCountHistoryDO">
        SELECT d.DATE,
        count(0) as concentra_group_count
        FROM margin.t_peer_sec_category_data d
        <where>
            d.${peerEnName} IS NOT NULL
            and d.${peerEnName} not like '%无集中度分组%'
            <if test="startDate != null">
                and  d.DATE >= #{startDate,jdbcType=DATE}
            </if>
        </where>
        GROUP BY d.DATE
        ORDER BY d.DATE DESC;
    </select>
</mapper>