<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.peer.mapper.PeerTableMapper">

    <select id="countMaxDateMarketNonCheckRecord" resultType="java.lang.Integer">
        SELECT count(0)
        from credit.${tableName} z
        WHERE z.if_market_checked = 1
          and z.sse_date =
        (SELECT max(DATE)
         FROM credit.t_rzrq_flag
        WHERE `table` = #{tableName,jdbcType=VARCHAR}
        <if test="mrgTrdDataType.code == 'collateral'">
            AND flag >= 1
            AND zsl_flag >= 1
        </if>
        <if test="mrgTrdDataType.code == 'underlying'">
            AND flag >= 1
            and rz_flag >= 1
            and rq_flag >= 1
        </if>
        <if test="mrgTrdDataType.code == 'category'">
            AND flag >= 1
            and c_flag >= 1
        </if>
        )
    </select>
</mapper>