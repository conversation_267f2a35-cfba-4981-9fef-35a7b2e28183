<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.peer.mapper.SecPeerInfoMapper">

    <update id="updateSecPeerInfoCollateral">
        INSERT INTO tj_middle_ground.t_sec_peer_info
        (sec_code, sec_name, `code`, date, sec_type, collateral_haircut, is_collateral, source)

        SELECT rsi.code,
               rsi.sec_name,
               rsi.sec_code,
               p.sse_date                   as date,
               rsi.sec_type,
               IF(replace(p.rate, '%', '') * 1 <![CDATA[ <= ]]> 1, replace(p.rate, '%', '') * 100,
                  replace(p.rate, '%', '')) as collateral_haircut,
               if(p.status = 1, 0, 1)       as is_collateral,
               #{cnName,jdbcType=VARCHAR}   as source
        from credit.${tableName} as p
                 INNER JOIN
             credit.t_rzrq_sec_info rsi
             on p.sec_code = rsi.code and p.market = rsi.source
        where p.enable_status = 0
          and p.if_market_checked = 0
          and p.sse_date =
              (SELECT max(date)
               from credit.t_rzrq_flag
               where `table` = #{tableName,jdbcType=VARCHAR}
                 and flag >= 1
                 and zsl_flag >= 1)

        ORDER BY rsi.sec_code asc
        ON DUPLICATE KEY UPDATE sec_code           = VALUES(sec_code),
                                sec_name           = VALUES(sec_name),
                                sec_type           = VALUES(sec_type),
                                date               = VALUES(date),
                                collateral_haircut = VALUES(collateral_haircut),
                                is_collateral      = VALUES(is_collateral);
    </update>

</mapper>
