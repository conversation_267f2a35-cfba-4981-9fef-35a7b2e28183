<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.peer.mapper.PeerTableDataFixLogMapper">
  <resultMap id="BaseResultMap" type="com.tjsj.modify.modules.peer.model.entity.PeerTableDataFixLogDO">
    <!--@mbg.generated-->
    <!--@Table credit.t_peer_table_data_fix_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

</mapper>