<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modify.modules.stock.mapper.StockUpdateUtilMapper">
    <update id="distinctHolderHistoryTable">
        update pledgedata.t_tenshareholderinfos
        set enable_status = 1
        WHERE id in
              (SELECT m.id
               from (SELECT min(id) as id
                     from pledgedata.t_tenshareholderinfos
                     GROUP BY stockId, date, px, number
                     having count(0) > 1) m);
    </update>

    <select id="selectNewLatestHolderList" resultType="LatestHolderInfoDO">
        SELECT tsi.StockId as sec_code,
        tsi.StockName as sec_name,
        tsi.shareholdersName as holder_name,
        tsi.date,
        tsi.Number as holding_number,
        round(tsi.pledgeRatio, 2) as pledge_ratio,
        tsi.px AS holder_order,
        round(tsi.ratio, 2) as shareholding_ratio,
        tsi.id as ref_id
        from (SELECT tsi.id
        from (SELECT stockId, max(date) as date
        from pledgedata.t_tenshareholderinfos
        WHERE date > DATE_SUB(CURRENT_DATE, INTERVAL 2 YEAR)
        and MONTH(date) IN (3, 6, 9, 12)
        AND DAY(date) = CASE
        WHEN MONTH(date) = 3 THEN 31
        WHEN MONTH(date) = 6 THEN 30
        WHEN MONTH(date) = 9 THEN 30
        WHEN MONTH(date) = 12 THEN 31 END
        <if test="newFilterSecCode != null and newFilterSecCode != ''">
            and stockId = #{newFilterSecCode,jdbcType=VARCHAR}
        </if>
        GROUP BY stockId) max_date
        INNER JOIN
        pledgedata.t_tenshareholderinfos tsi
        on max_date.stockId = tsi.stockId and tsi.date >= max_date.date
        <if test="newFilterSecCode != null and newFilterSecCode != ''">
            and tsi.stockId = #{newFilterSecCode,jdbcType=VARCHAR}
        </if>
        INNER JOIN
        tj_middle_ground.t_stock_info i
        on tsi.StockId = i.stock_id and tsi.Date >= i.startDate
        GROUP BY tsi.StockId, tsi.ShareholdersName) new_record
        INNER JOIN
        pledgedata.t_tenshareholderinfos tsi
        on new_record.id = tsi.id
        where tsi.enable_status = 0
        ORDER BY tsi.stockId, tsi.px
    </select>

    <select id="selectNewLatestHolderList2" resultType="LatestHolderInfoDO">
        SELECT ti.StockId as sec_code,
        ti.StockName as sec_name,
        ti.ShareholdersName as holder_name,
        ti.date,
        ti.Number as holding_number,
        round(ti.PledgeRatio, 2) as pledge_ratio,
        ti.Px as holder_order,
        round(ti.Ratio, 2) as shareholding_ratio,
        ti.id as ref_id
        from (SELECT ti.StockId, max(ti.date) as max_date
        from (SELECT si.stock_id
        FROM tj_middle_ground.t_stock_info si
        WHERE si.`status` = 0
        <if test="newFilterSecCode != null and newFilterSecCode != ''">
            and si.stock_id = #{newFilterSecCode,jdbcType=VARCHAR}
        </if>
        <if test="newSecCodeList != null and newSecCodeList.size() != 0">
            and si.stock_id not in
            <foreach collection="newSecCodeList" item="secCode" separator="," open="(" close=")">
                #{secCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY si.startDate asc) si
        INNER JOIN
        pledgedata.t_tenshareholderinfos ti
        on si.stock_id = ti.StockId and ti.enable_status = 0
        <if test="newFilterSecCode != null and newFilterSecCode != ''">
            and ti.StockId = #{newFilterSecCode,jdbcType=VARCHAR}
        </if>
        GROUP BY ti.StockId
        ORDER BY ti.StockId) md
        INNER JOIN
        pledgedata.t_tenshareholderinfos ti
        ON md.StockId = ti.StockId and md.max_date = ti.date
    </select>

    <update id="distinctHolderReductionTable">
        update credit.t_dfcf_equity_pledge
        set enable_status = 1
        WHERE id in
              (SELECT m.id
               from (SELECT min(id) as id
                     from credit.t_dfcf_equity_pledge jian
                     GROUP BY jian.code, jian.holder_name, jian.change_count, jian.start_date, jian.end_date
                     having count(0) > 1) m);
    </update>

    <delete id="distinctCriminalRecordTable">
        DELETE
        from pledgedata.t_criminalrecords
        WHERE id in
              (SELECT A.id
               from (SELECT min(id) id
                     FROM pledgedata.`t_criminalrecords`
                     GROUP BY stockId, AnnDate, title, DealingWithPeople, DisposeTheType, Irregularities, ReplyContent
                     HAVING count(0) > 1) A);
    </delete>


    <update id="updateHolderShareFrozenTableFrozenPersonField">
        <!--        UPDATE pledgedata.t_frozen
                SET frozenPerson = REPLACE(REPLACE(REPLACE(REPLACE(frozenPerson, '1', ''), '2', ''), '3', ''), '4', '');-->
    </update>

    <select id="selectNewHolderShareFrozenList" resultType="HolderShareFrozenDO">
        SELECT code    as sec_code,
               frozenPerson,
               pubDate as publishDate,
               startDate,
               endDate,
               frozenNumber,
               unfrozenNumber,
               frozenReason,
               frozenShareNatureId,
               frozenTotal_ratio,
               freezeApplicant,
               freezeExecutor,
               unfrozenDetail
        from pledgedata.t_frozen as f
        WHERE pubDate > date_sub(now(), INTERVAL 3 YEAR)
           or startDate > date_sub(now(), INTERVAL 3 YEAR);
    </select>

    <select id="listHolderFrozenInfo" resultType="HolderShareFrozenLabelDO">
        select fro.stock_id as sec_code,
        fro.stock_name as sec_name,
        CAST(fro.frozenNumber AS SIGNED) AS frozenNumber,
        fro.person as frozen_person,
        fro.info_describe,
        fro.date
        from
        (SELECT info.stock_id,
        info.stock_name,
        fro.frozen_person as person,
        sum(fro.frozen_number),
        IFNULL(sum(fro.frozen_number), 0) - IFNULL(sum(fro.unfrozen_number), 0) as frozenNumber,
        GROUP_CONCAT(IF(fro.frozen_reason LIKE '%解除冻结%', NULL, fro.frozen_reason)
        SEPARATOR ' ') as info_describe,
        max(fro.publish_date) as date
        FROM tj_middle_ground.t_holder_share_frozen as fro
        INNER JOIN
        tj_middle_ground.t_stock_info as info
        on fro.sec_code = info.stock_id
        WHERE (fro.start_date > date_sub(now(), INTERVAL 3 YEAR) or fro.start_date is null)
        and (fro.frozen_reason like '%解除冻结%' or
        (fro.frozen_reason not like '%解除冻结%' and (fro.end_date is null or fro.end_date > CURRENT_DATE)))
        GROUP BY fro.sec_code, fro.frozen_person) as fro
        INNER JOIN
        <if test="holderType == '控股股东'">
            (SELECT sec_code as stockId, holder_name as name
            from pledgedata.t_latest_holder_info
            WHERE holder_order = 1
            ORDER BY sec_code) as conHold
            on fro.stock_id = conHold.stockId
            INNER JOIN
            (SELECT code as stockId, holder_name as ControlShareholder
            from credit.t_ths_control
            WHERE holder_name is not null
            and leaves = 0
            and type = '控制股东') as control
            on fro.stock_id = control.StockId
            where (fro.person like concat('%', control.ControlShareholder, '%') or
            control.ControlShareholder like concat('%', fro.person, '%')
            or (fro.info_describe REGEXP '控股股东' and
            (fro.person like concat('%', conHold.name, '%') or conHold.name like concat('%', fro.person,
            '%'))));
        </if>
        <if test="holderType == '大股东'">
            (select holders.*
            from (select sec_code as stockId, holder_name as name, holding_number as number
            from pledgedata.t_latest_holder_info) as holders
            LEFT JOIN
            (select code as stockId, holder_name as name
            from credit.t_ths_control
            WHERE holder_name is not null
            and leaves = 0
            and type = '控制股东') as control
            on holders.stockId = control.stockId and
            (holders.name = control.name
            or (holders.`name` like concat('%', control.`name`, '%') or
            control.`name` like concat('%', holders.`name`, '%')
            ))
            where control.stockId is null)
            as holder
            on fro.stock_id = holder.stockId and
            ( fro.`person` like concat('%',holder.`name`,'%') or holder.`name` like concat('%',fro.`person`,'%') )
        </if>
    </select>

    <select id="getSecFrozenPersonLabelInfo" resultType="HolderShareFrozenLabelDO">
        select holding_num.stockId as secCode
        , info.stock_name as secName
        , person.frozenPerson
        <choose>
            <when test="holderType == '控股股东'">
                , case
                when round(person.frozenNumber / holding_num.number, 2) > 0 and
                round(person.frozenNumber / holding_num.number, 2) <![CDATA[ < ]]> 0.7
                then '控股股东股份部分冻结'
                when round(person.frozenNumber / holding_num.number, 2) >= 0.7 and
                round(person.frozenNumber / holding_num.number, 2) <![CDATA[ < ]]> 1
                then '控股股东股份高比例冻结'
                when round(person.frozenNumber / holding_num.number, 2) >= 1
                then '控股股东股份全额及轮候冻结' end as labelName
            </when>
            <when test="holderType == '大股东'">
                , case
                when round(person.frozenNumber / holding_num.number, 2) > 0 and
                round(person.frozenNumber / holding_num.number, 2) <![CDATA[ < ]]> 0.7
                then '大股东股份部分冻结'
                when round(person.frozenNumber / holding_num.number, 2) >= 0.7 and
                round(person.frozenNumber / holding_num.number, 2) <![CDATA[ < ]]> 1
                then '大股东股份高比例冻结'
                when round(person.frozenNumber / holding_num.number, 2) >= 1
                then '大股东股份全额及轮候冻结' end as labelName
            </when>
        </choose>
        from (select #{secCode,jdbcType=VARCHAR} as stockId,
        #{frozenPerson,jdbcType=VARCHAR} as frozenPerson,
        #{frozenNumber,jdbcType=BIGINT} as frozenNumber) person
        INNER JOIN
        tj_middle_ground.t_stock_info info
        on person.stockId = info.stock_id and info.status = 0 and info.stock_id =
        #{secCode,jdbcType=VARCHAR}
        INNER JOIN
        (SELECT holder_name as name, holding_number as number, sec_code as stockId
        from pledgedata.t_latest_holder_info
        where sec_code = #{secCode,jdbcType=VARCHAR}) as holding_num
        on (person.frozenPerson like concat('%', holding_num.name, '%') or
        holding_num.name like concat('%', person.frozenPerson, '%'))
    </select>

    <update id="updateStockMarketPledgeRatio">
        UPDATE tj_middle_ground.t_stock_info si
            LEFT JOIN (SELECT t.StockId,
                              t.Ratio
                       FROM pledgedata.t_pledgemarkettotal t
                                INNER JOIN (SELECT MAX(DATE) AS MaxDate FROM pledgedata.t_pledgemarkettotal) tmd
                                           ON t.DATE = tmd.MaxDate) lp ON si.stock_id = lp.StockId
        SET si.pledge_ratio = IFNULL(lp.Ratio, 0)
        WHERE si.stock_id IS NOT NULL;
    </update>

    <update id="updateStockMarketCollateralRatio">
        update tj_middle_ground.t_stock_info si
            LEFT JOIN
            (SELECT h.`code` as stock_id, h.ratio
             from (SELECT d.`code`, max(d.date) as date
                   from (SELECT d.DATE
                         FROM credit.t_rzrq_dbw d
                         GROUP BY d.DATE
                         ORDER BY d.DATE DESC
                         limit 2) md
                            INNER JOIN
                        credit.t_rzrq_dbw d
                        on md.date = d.date
                   GROUP BY d.`code`) md
                      INNER JOIN
                  credit.t_rzrq_dbw h
                  on md.date = h.date and md.`code` = h.`Code`) h
            ON si.stock_id = h.stock_id
        SET si.market_collateral_ratio = IFNULL(h.ratio, 0)
        WHERE si.stock_id IS NOT NULL;
    </update>

    <delete id="deleteFrozenTableDuplicateData">
        DELETE
        FROM pledgedata.t_frozen
        WHERE id IN (SELECT *
                     FROM (SELECT MIN(f.id) AS id
                           FROM pledgedata.t_frozen f
                           GROUP BY f.`Code`,
                                    f.FrozenPerson,
                                    f.PubDate,
                                    f.StartDate,
                                    f.EndDate,
                                    f.FrozenNumber,
                                    f.UnfrozenNumber,
                                    f.FrozenReason,
                                    f.FrozenShareNatureId,
                                    f.FrozenShareNature,
                                    f.FrozenTotal_ratio,
                                    f.FreezeApplicant,
                                    f.FreezeExecutor,
                                    f.FrozenPerson_id,
                                    f.UnfrozenDetail
                           HAVING COUNT(*) > 1) AS temp_ids);
    </delete>
</mapper>