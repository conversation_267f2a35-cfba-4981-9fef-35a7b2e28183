# 开发环境配置，开发团队在本地开发环境上进行开发，使用本地数据库进行开发，数据库连接信息如下：
spring:
  datasource:
    dynamic:
      #seata: true
      primary: tj_middle_ground
      datasource:
        tj_middle_ground: #数据源tj_middle_ground
          url: *****************************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: aDqe1(345xaawPZv
          druid:
            validation-query: SELECT 1 FROM DUAL
    druid:
      initial-size: 50
      max-active: 6000
      min-idle: 10
      max-wait: 600000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
      filter:
        wall:
          enabled: true
          config:
            multi-statement-allow: true
        stat:
          log-slow-sql: true
          slow-sql-millis: 10
          merge-sql: false
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid配置
  # 开发环境的redis配置
  redis:
    database: 0
    host: localhost
    port: 6379
    password: tarkinData1qaz2wsx
    timeout: 10000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 5
        max-idle: 25
        min-idle: 10

# 项目自定义配置
tarkin:
  # 项目运行环境
  env: tarkin
  save-log: false # 是否保存日志到数据库，默认true

# 日志打印配置
logging:
  level:
    root: INFO
