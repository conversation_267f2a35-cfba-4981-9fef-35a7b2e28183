<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- ========================================
         📋 日志配置文件
         🎯 功能：分离WARN和ERROR级别日志到不同文件
         📅 更新：2025-07-01
         🔧 优化：减少重复配置，提高可维护性
         ======================================== -->

    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <!-- ========================================
         🔧 全局配置参数 (统一管理所有配置)
         ======================================== -->
    <property name="SERVICE_NAME" value="modify-service"/>  <!-- 服务名称 -->
    <property name="LOG_PATTERN" value="%date %level [%thread] %logger{10} [%file:%line] %msg%n"/>  <!-- 日志格式 -->
    <property name="LOG_CHARSET" value="UTF-8"/>  <!-- 日志编码 -->
    <property name="MAX_FILE_SIZE" value="10MB"/>  <!-- 单文件大小 -->
    <property name="MAX_HISTORY" value="30"/>  <!-- 保留天数 -->
    <property name="TOTAL_SIZE_CAP" value="5GB"/>  <!-- 总大小限制 -->
    <property name="LOG_DIR" value="./logs"/>  <!-- 日志目录 -->
    <!-- 🎯 环境配置：如果获取不到spring.profiles.active，则默认使用dev -->
    <property name="PROFILE" value="${spring.profiles.active:-dev}"/>

    <!-- ========================================
         🔇 第三方库日志级别控制
         ======================================== -->
    <logger name="c.n.d.s.r.aws.ConfigClusterResolver" level="ERROR"/>
    <logger name="org.springframework.web" level="ERROR"/>
    <logger name="org.springboot.sample" level="ERROR"/>

    <!-- ========================================
         📁 日志输出器定义 (DRY原则)
         ======================================== -->

    <!-- 🔴 ERROR级别日志输出器 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件路径 -->
        <file>${LOG_DIR}/${SERVICE_NAME}-${PROFILE}_error.log</file>
        <!-- 日志格式 -->
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${LOG_DIR}/${SERVICE_NAME}/${SERVICE_NAME}-${PROFILE}_error-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <!-- 基于文件大小和时间的滚动策略 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 保留日志数量 -->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <!-- 日志过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 🟡 WARN级别日志输出器 -->
    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${SERVICE_NAME}-${PROFILE}_warn.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${SERVICE_NAME}/${SERVICE_NAME}-${PROFILE}_warn-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ========================================
         📝 环境无关的统一日志配置
         ======================================== -->
    <root level="WARN">
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="WARN_FILE"/>
    </root>


    <!-- ###################### 跨环境公共配置 ###################### -->
    <!-- 通用监控日志模板（开发/生产共用） -->
    <appender name="CODE_MONITOR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/${SERVICE_NAME}-code-monitor.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/${SERVICE_NAME}/${SERVICE_NAME}-code-monitor-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- 每个日志文件最大10MB -->
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 保留最近30天的日志文件 -->
            <maxHistory>30</maxHistory>
            <!-- 日志文件总大小不能超过1GB -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>


    <!-- 设置专用 Logger 级别及输出 -->
    <!--additivity="true"：表示该 logger 继承父 logger 的日志级别-->
    <logger name="codeMonitor" level="INFO" additivity="true">
        <appender-ref ref="CODE_MONITOR"/>
    </logger>

    <!-- ========================================
         📊 配置文件结构说明
         ========================================

         📁 文件结构：
         ├── 🔧 全局配置参数
         ├── 🔇 第三方库日志级别控制
         ├── 📁 日志输出器定义
         └── 📝 统一日志配置

         📋 日志文件输出：
         ├── 🔴 ERROR: ${SERVICE_NAME}-{profile}_error.log
         └── 🟡 WARN:  ${SERVICE_NAME}-{profile}_warn.log

         ⚙️ 滚动策略：
         ├── 📏 单文件大小: ${MAX_FILE_SIZE}
         ├── 📅 保留天数: ${MAX_HISTORY}天
         └── 💾 总大小限制: ${TOTAL_SIZE_CAP}

         🔄 环境支持：
         ├── 🛠️ dev/test/local 环境
         └── 🏭 prod 生产环境

         ======================================== -->

</configuration>


