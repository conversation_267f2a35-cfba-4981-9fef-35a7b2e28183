package com.tjsj.modify.modules.stock.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * LatestHolderInfoDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/09/08
 * @description 最新十大股东表
 */
@Data
@Accessors(chain = true)
@TableName("pledgedata.t_latest_holder_info")
@Alias(value = "LatestHolderInfoDO")
@Schema(name = "LatestHolderInfoDO", description = "最新十大股东表")
public class LatestHolderInfoDO {

    /**
     * 主键
     */
    @Schema(description = "主键", required = true)
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 证券代码
     */
    @Schema(description = "证券代码", required = true, example = "000001")
    @TableField(value = "sec_code")
    private String secCode;

    /**
     * 证券名称
     */
    @Schema(description = "证券名称", required = true, example = "平安银行")
    @TableField(value = "sec_name")
    private String secName;

    /**
     * 股东名称
     */
    @Schema(description = "股东名称", required = true, example = "大唐集团")
    @TableField(value = "holder_name")
    private String holderName;

    /**
     * 日期
     */
    @Schema(description = "日期", required = true, example = "2021-01-01")
    @TableField(value = "date")
    private LocalDate date;

    /**
     * 持股数量
     */
    @Schema(description = "持股数量", required = true, example = "100000000")
    @TableField(value = "holding_number")
    private Long holdingNumber;

    /**
     * 股份质押比例
     */
    @Schema(description = "股份质押比例", required = true, example = "11.1")
    @TableField(value = "pledge_ratio")
    private BigDecimal pledgeRatio;

    /**
     * 持股顺序
     */
    @Schema(description = "持股顺序", required = true, example = "1")
    @TableField(value = "holder_order")
    private Integer holderOrder;

    /**
     * 股份占总股本比例
     */
    @Schema(description = "股份占总股本比例", required = true, example = "10.1")
    @TableField(value = "shareholding_ratio")
    private BigDecimal shareholdingRatio;

    /**
     * 关联id
     */
    @Schema(description = "关联id", required = true, example = "1")
    @TableField(value = "ref_id")
    private Long refId;


    @JSONField(serialize = false)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @JSONField(serialize = false)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
