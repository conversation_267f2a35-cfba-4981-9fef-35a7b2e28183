package com.tjsj.modify.modules.margin.business.mapper;

import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdSecDO;
import com.tjsj.modules.base.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * ExchangeRuleThresholdSecMapper
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/02/21
 * @description 交易所规则阈值证券映射器
 */
@Mapper
public interface ExchangeRuleThresholdSecMapper extends IBaseMapper<ExchangeRuleThresholdSecDO> {
    int insertOrUpdate(ExchangeRuleThresholdSecDO record);

    int insertOrUpdateSelective(ExchangeRuleThresholdSecDO record);

    /**
     * 选择股票折算率上限
     *
     * @return {@link List }<{@link ExchangeRuleThresholdSecDO }>
     * <AUTHOR> Ye
     * @date 2025/02/21
     */
    Set<ExchangeRuleThresholdSecDO> selectStockHaircutUpperLimit();

    /**
     * 选择其他证券折算率上面限制
     *
     * @return {@link Set }<{@link ExchangeRuleThresholdSecDO }>
     * <AUTHOR>
     * @date 2025/02/21
     */
    Set<ExchangeRuleThresholdSecDO> selectOtherSecHaircutUpperLimit();

}