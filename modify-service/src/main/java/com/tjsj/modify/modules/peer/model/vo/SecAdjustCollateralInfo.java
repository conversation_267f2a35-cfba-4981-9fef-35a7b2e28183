package com.tjsj.modify.modules.peer.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.annotation.JSONField;
import com.tjsj.common.annotation.calculate.CalculateField;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

/**
 * SecAdjustCollateralInfo
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/20 10:37
 * @description 同业券商-担保品调整信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@FieldNameConstants
@Schema(description = "同业券商-担保品调整信息")
public class SecAdjustCollateralInfo extends SecAdjustBaseModel {

    /**
     * 担保品调出数量
     */
    @JSONField(ordinal = 10)
    @CalculateField(value = PeerConsts.COLLATERAL_OUT)
    @Schema(description = "担保品调出数量")
    @ExcelProperty(value = "担保品调出数量", order = 100)
    private Integer collateralOutCount = 0;

    /**
     * 折算率调低数量
     */
    @JSONField(ordinal = 20)
    @CalculateField(value = PeerConsts.COLLATERAL_HAIRCUT_REDUCE)
    @Schema(description = "折算率调低数量")
    @ExcelProperty(value = "折算率调低数量", order = 200)
    private Integer collateralHaircutReduceCount = 0;

    /**
     * 折算率调高数量
     */
    @JSONField(ordinal = 30)
    @CalculateField(value = PeerConsts.COLLATERAL_HAIRCUT_INCREASE)
    @Schema(description = "折算率调高数量")
    @ExcelProperty(value = "折算率调高数量", order = 300)
    private Integer collateralHaircutIncreaseCount = 0;



}
