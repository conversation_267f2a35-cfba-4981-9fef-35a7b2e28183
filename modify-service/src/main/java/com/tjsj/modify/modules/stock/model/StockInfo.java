package com.tjsj.modify.modules.stock.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tjsj.common.annotation.calculate.CalculateField;
import com.tjsj.common.enums.basic.CommonStatusEnum;
import com.tjsj.common.enums.basic.DataUnit;
import com.tjsj.modify.modules.market.model.entity.StockMarketHisDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * StockInfo
 *
 * <AUTHOR> Ye
 * @date 2024/07/08
 * @description 股票基本信息表
 */
@Data
@Accessors(chain = true)
@Alias(value = "StockInfo")
@TableName("tj_middle_ground.t_stock_info")
@Schema(name = "StockInfo", description = "股票基本信息表")
public class StockInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 股票代码
     */
    @Schema(description = "股票代码")
    @TableId("stock_id")
    @JSONField(ordinal = 10)
    @ExcelIgnore
    private String stockId;

    /**
     * 证券代码带后缀
     */
    @Schema(description = "证券代码带后缀")
    @TableField("sec_code")
    @ExcelProperty("证券代码")
    @JSONField(ordinal = 20)
    private String secCode;

    /**
     * 股票名称
     */
    @Schema(description = "股票名称")
    @TableField("stock_name")
    @JSONField(ordinal = 30)
    private String stockName;

    /**
     * 昨日收盘价
     */
    @CalculateField(fieldName = StockMarketHisDO.CLOSE_PRICE_FIELD)
    @Schema(description = "昨日收盘价")
    @TableField("closes")
    @JSONField(ordinal = 40)
    private BigDecimal closePrice;

    /**
     * 总市值
     */
    @CalculateField(fieldName = StockMarketHisDO.TOTAL_AMOUNT_FIELD, dataUnit = DataUnit.HUNDRED_MILLION)
    @Schema(description = "总市值")
    @TableField("total_amount")
    @JSONField(ordinal = 50)
    private BigDecimal totalAmount;

    /**
     * 流通市值
     */
    @CalculateField(fieldName = StockMarketHisDO.CIRCULATION_AMOUNT_FIELD, dataUnit = DataUnit.HUNDRED_MILLION)
    @Schema(description = "流通市值")
    @TableField("circulate_total_amount")
    @JSONField(ordinal = 60)
    private BigDecimal circulateTotalAmount;

    /**
     * 滚动市盈率
     */
    @CalculateField(fieldName = StockMarketHisDO.ROLLING_PE_FIELD)
    @Schema(description = "滚动市盈率")
    @TableField("pe")
    @JSONField(ordinal = 70)
    private BigDecimal pe;

    /**
     * 动态市盈率
     */
//    @CalculateField(fieldName = StockMarketHisDO.DYNAMIC_PE_FIELD)
//    @Schema(description = "动态市盈率")
//    @TableField(value = "dynamic_pe")
//    @JSONField(ordinal = 75)
//    private BigDecimal dynamicPe;

    /**
     * 静态市盈率
     */
    @CalculateField(fieldName = StockMarketHisDO.STATIC_PE_FIELD)
    @Schema(description = "静态市盈率")
    @TableField(value = "static_pe")
    @JSONField(ordinal = 80)
    private BigDecimal staticPe;

    /**
     * 市净率
     */
    @CalculateField(fieldName = StockMarketHisDO.PB_FIELD)
    @Schema(description = "市净率(PB)")
    @TableField("pb")
    @JSONField(ordinal = 90)
    private BigDecimal pb;

    /**
     * 股东人均市值
     */
    @Schema(description = "股东人均市值")
    @TableField(value = "total_amount_per")
    @JSONField(ordinal = 100)
    private BigDecimal totalAmountPer;

    /**
     * 系统评级
     */
    @Schema(description = "系统评级")
    @TableField("level")
    @JSONField(ordinal = 101)
    private String level;

    /**
     * 一级行业id
     */
    @Schema(description = "一级行业id", example = "00001048")
    @TableField("industry_id")
    @JSONField(ordinal = 110)
    private String industryId;

    /**
     * 二级行业id
     */
    @Schema(description = "二级行业id", example = "0000104803")
    @TableField("industry_id_two")
    @JSONField(ordinal = 120)
    private String industryIdTwo;

    /**
     * 一级行业名称
     */
    @Schema(description = "一级行业名称", example = "银行")
    @TableField("industry_name")
    private String industryName;

    /**
     * 二级行业名称
     */
    @Schema(description = "二级行业名称", example = "股份制银行Ⅱ")
    @TableField("industry_name_two")
    private String industryNameTwo;

    /**
     * 最新行情日期
     */
    @Schema(description = "最新行情日期")
    @TableField("date")
    private LocalDate date;

    /**
     * 股票类型
     */
    @TableField(value = "stockType")
    @Schema(description = "股票类型")
    private String stockType;

    /**
     * 股票状态
     */
    @TableField(value = "`status`")
    @Schema(description = "在市状态,0:上市 1:退市")
    private CommonStatusEnum status;

    @Schema(description = "市场融资余额")
    @TableField(value = "rzye")
    private BigDecimal rzye;

    @Schema(description = "市场融券余量")
    @TableField(value = "rqyl")
    private Integer rqyl;

    @TableField(exist = false)
    private String labels;

    @TableField(exist = false)
    private List<String> labelList;

    @TableField(exist = false)
    private List<Map<String, Object>> financialTrend;

    /**
     * 开始日期
     */
    @Schema(description = "上市起始日期")
    @TableField(value = "startDate")
    private String startDate;

    /**
     * 结束日期
     */
    @Schema(description = "上市终止日期")
    @TableField(value = "endDate")
    private String endDate;

    @Schema(description = "是否注册制", example = "0:否 1:是")
    @TableField(value = "ifRegi")
    private Integer ifRegi;

    @Schema(description = "创建时间，默认是当前时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @JSONField(serialize = false)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
