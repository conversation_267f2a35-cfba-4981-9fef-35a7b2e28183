package com.tjsj.modify.modules.market.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ExchangeStaticPeSecDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/1/20 9:51
 * @description 交易所静态市盈率规则折算率调0证券
 */
@Schema(description = "交易所静态市盈率规则折算率调0证券")
@Data
@Accessors(chain = true)
@Alias(value = "ExchangeStaticPeSecDO")
@TableName(value = "tj_middle_ground.t_exchange_static_pe_sec")
public class ExchangeStaticPeSecDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    /**
     * 证券名称
     */
    @TableField(value = "sec_name")
    @Schema(description = "证券名称")
    private String secName;

    /**
     * 最新静态市盈率
     */
    @TableField(value = "static_pe")
    @Schema(description = "最新静态市盈率")
    private BigDecimal staticPe;

    /**
     * 交易日期
     */
    @TableField(value = "date")
    @Schema(description = "交易日期")
    private LocalDate date;


    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}