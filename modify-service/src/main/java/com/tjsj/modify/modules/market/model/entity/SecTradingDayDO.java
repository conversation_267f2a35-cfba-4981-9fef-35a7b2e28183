package com.tjsj.modify.modules.market.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * SecTradingDayDO
 *
 * <AUTHOR>
 * @version 1.0.0@description 证券交易日信息
 * @date 2024/11/11 14:20
 * @description SecTradingDayDO
 */
@Schema(description = "证券交易日信息")
@Data
@Accessors(chain = true)
@TableName(value = "pledgedata.t_sec_trading_day")
@Alias(value = "SecTradingDayDO")
public class SecTradingDayDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    @TableField(value = "`date`")
    @Schema(description = "")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 交易日序号
     */
    @TableField(value = "trading_number")
    @Schema(description = "交易日序号")
    private Integer tradingNumber;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}