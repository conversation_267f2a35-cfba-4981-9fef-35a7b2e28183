package com.tjsj.modify.modules.peer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modify.modules.peer.mapper.PeerTableDataFixLogMapper;
import com.tjsj.modify.modules.peer.model.entity.PeerTableDataFixLogDO;
import com.tjsj.modify.modules.peer.service.PeerTableDataFixLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/4/16 20:12
 * @description
 */
@Service
public class PeerTableDataFixLogServiceImpl extends ServiceImpl<PeerTableDataFixLogMapper, PeerTableDataFixLogDO>
		implements PeerTableDataFixLogService {

	@Resource
	private PeerTableDataFixLogMapper peerTableDataFixLogMapper;


}
