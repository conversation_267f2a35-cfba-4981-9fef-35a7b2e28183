package com.tjsj.modify.modules.peer.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.enums.basic.CommonStatusEnum;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.peer.mapper.PeerDataMapper;
import com.tjsj.modify.modules.peer.mapper.PeerSecCountHistoryMapper;
import com.tjsj.modify.modules.peer.model.entity.PeerSecCountHistoryDO;
import com.tjsj.modify.modules.peer.model.entity.PeerSecuritiesSetting;
import com.tjsj.modify.modules.peer.service.PeerSecCountHistoryService;
import com.tjsj.modify.modules.peer.service.PeerSecuritiesSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/2/10 21:06
 * @description
 */

@Service
@Slf4j
public class PeerSecCountHistoryServiceImpl extends ServiceImpl<PeerSecCountHistoryMapper, PeerSecCountHistoryDO>
        implements PeerSecCountHistoryService {

    @Resource
    private PeerSecuritiesSettingService peerSecuritiesSettingService;

    @Resource
    private PeerDataMapper peerDataMapper;

    @Override
    public void updatePeerSecCountHistory(Integer startBeforeDays) {
        // TODO:有时间可以修改成updateOrSave的逻辑

        List<PeerSecuritiesSetting> peerConfigList =
                peerSecuritiesSettingService.list(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                        .eq(PeerSecuritiesSetting::getIfColumnShow, CommonStatusEnum.ENABLE));


        List<Integer> removeRecordIdList = new ArrayList<>();
        List<PeerSecCountHistoryDO> newRecordList = new ArrayList<>();

        for (PeerSecuritiesSetting peerConfig : peerConfigList) {
            // 获取LocalDate类型的七天前日期
            LocalDate startDate = startBeforeDays == null ? null : LocalDate.now().minusDays(startBeforeDays);
            String peerEnName = peerConfig.getEnName();

            // 查询同业新记录
            List<PeerSecCountHistoryDO> peerNewRecordList = this.getPeerNewRecordList(peerEnName, startDate);

            // 查询旧记录
            List<PeerSecCountHistoryDO> oldRecordList =
                    this.list(Wrappers.<PeerSecCountHistoryDO>lambdaQuery()
                            .select(PeerSecCountHistoryDO::getId, PeerSecCountHistoryDO::getDate,
                                    PeerSecCountHistoryDO::getCollateralCount,
                                    PeerSecCountHistoryDO::getFinanceTargetCount,
                                    PeerSecCountHistoryDO::getShortSellTargetCount,
                                    PeerSecCountHistoryDO::getConcentraGroupCount)
                            .eq(PeerSecCountHistoryDO::getSource, peerEnName)
                            .ge(startDate != null, PeerSecCountHistoryDO::getDate, startDate));


            // 分别构建旧列表和新列表的 key -> record 映射，避免重复计算唯一 key
            Map<String, PeerSecCountHistoryDO> oldKeyMap = oldRecordList.stream()
                    .collect(Collectors.toMap(
                            record -> ModifyDataUtil.buildUniqueKey(
                                    record.getDate(),
                                    record.getCollateralCount(),
                                    record.getFinanceTargetCount(),
                                    record.getShortSellTargetCount(),
                                    record.getConcentraGroupCount()
                            ),
                            Function.identity()
                    ));
            Map<String, PeerSecCountHistoryDO> newKeyMap = peerNewRecordList.stream()
                    .collect(Collectors.toMap(
                            record -> ModifyDataUtil.buildUniqueKey(
                                    record.getDate(),
                                    record.getCollateralCount(),
                                    record.getFinanceTargetCount(),
                                    record.getShortSellTargetCount(),
                                    record.getConcentraGroupCount()
                            ),
                            Function.identity()
                    ));


            // 删除旧列表中不存在于新列表中的记录
            List<Integer> idsToRemove = oldKeyMap.entrySet().stream()
                    .filter(entry -> !newKeyMap.containsKey(entry.getKey()))
                    .map(entry -> entry.getValue().getId())
                    .toList();

            if (!idsToRemove.isEmpty()) {
                removeRecordIdList.addAll(idsToRemove);
            }

            // 插入新列表中不存在于旧列表中的记录
            List<PeerSecCountHistoryDO> recordsToInsert = newKeyMap.entrySet().stream()
                    .filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
                    .map(Map.Entry::getValue)
                    .toList();

            if (!recordsToInsert.isEmpty()) {
                newRecordList.addAll(recordsToInsert);
            }


        }

        if (!removeRecordIdList.isEmpty()) {
            this.removeByIds(removeRecordIdList);
        }
        if (!newRecordList.isEmpty()) {
            this.baseMapper.insertBatchSomeColumn(newRecordList);
        }

    }

    /**
     * 获取同业新记录列表
     *
     * @param peerEnName 同业en名称
     * @param startDate  开始日期
     * @return {@link List }<{@link PeerSecCountHistoryDO }>
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    private List<PeerSecCountHistoryDO> getPeerNewRecordList(String peerEnName, LocalDate startDate) {

        List<PeerSecCountHistoryDO> collateralRecordList =
                this.baseMapper.selectNewCollateralCountRecord(peerEnName, startDate);
        List<PeerSecCountHistoryDO> financingTargetRecordList =
                this.baseMapper.selectNewFinancingTargetCountRecord(peerEnName, startDate);
        List<PeerSecCountHistoryDO> shortSellTargetRecordList =
                this.baseMapper.selectNewShortSellTargetCountRecord(peerEnName, startDate);
        List<PeerSecCountHistoryDO> concentraGroupRecordList =
                this.baseMapper.selectNewConcentraGroupCountRecord(peerEnName, startDate);
        List<PeerSecCountHistoryDO> mergedRecordList =
                this.mergeRecordsByDate(collateralRecordList, financingTargetRecordList, shortSellTargetRecordList,
                        concentraGroupRecordList);

        mergedRecordList.forEach(record -> record.setSource(peerEnName)
                .setCollateralCount(record.getCollateralCount() == null ? 0 : record.getCollateralCount())
                .setFinanceTargetCount(record.getFinanceTargetCount() == null ? 0 : record.getFinanceTargetCount())
                .setShortSellTargetCount(record.getShortSellTargetCount() == null ? 0 :
                        record.getShortSellTargetCount())
                .setConcentraGroupCount(record.getConcentraGroupCount() == null ? 0 : record.getConcentraGroupCount()));
        return mergedRecordList;

    }

    /**
     * 按日期合并记录
     *
     * @param collateralRecordList      担保品记录列表
     * @param financingTargetRecordList 融资目标记录列表
     * @param shortSellTargetRecordList 卖空目标记录列表
     * @param concentraGroupRecordList  集中度分组记录列表
     * @return {@link List }<{@link PeerSecCountHistoryDO }>
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    public List<PeerSecCountHistoryDO> mergeRecordsByDate(
            List<PeerSecCountHistoryDO> collateralRecordList,
            List<PeerSecCountHistoryDO> financingTargetRecordList,
            List<PeerSecCountHistoryDO> shortSellTargetRecordList,
            List<PeerSecCountHistoryDO> concentraGroupRecordList) {

        // 使用 Map 以日期为键，存储合并后的记录
        Map<LocalDate, PeerSecCountHistoryDO> mergedMap = new HashMap<>();

        // 合并担保证券数量记录
        for (PeerSecCountHistoryDO record : collateralRecordList) {
            mergedMap.computeIfAbsent(record.getDate(), date -> new PeerSecCountHistoryDO().setDate(date))
                    .setCollateralCount(record.getCollateralCount());
        }

        // 合并融资标的数量记录
        for (PeerSecCountHistoryDO record : financingTargetRecordList) {
            mergedMap.computeIfAbsent(record.getDate(), date -> new PeerSecCountHistoryDO().setDate(date))
                    .setFinanceTargetCount(record.getFinanceTargetCount());
        }

        // 合并融券标的数量记录
        for (PeerSecCountHistoryDO record : shortSellTargetRecordList) {
            mergedMap.computeIfAbsent(record.getDate(), date -> new PeerSecCountHistoryDO().setDate(date))
                    .setShortSellTargetCount(record.getShortSellTargetCount());
        }

        // 合并集中度分组数量记录
        for (PeerSecCountHistoryDO record : concentraGroupRecordList) {
            mergedMap.computeIfAbsent(record.getDate(), date -> new PeerSecCountHistoryDO().setDate(date))
                    .setConcentraGroupCount(record.getConcentraGroupCount());
        }

        // 将 Map 转换回 List 并返回
        return mergedMap.values().stream()
                .sorted(Comparator.comparing(PeerSecCountHistoryDO::getDate)) // 按日期排序
                .collect(Collectors.toList());
    }

}
