package com.tjsj.modify.modules.stock.service;

/**
 * StockUpdateUtilService
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/2/8 21:50
 * @description
 */
public interface StockUpdateUtilService {
    /**
     * 去重股东减持公告表 credit.t_dfcf_equity_pledge
     *
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    void distinctHolderReductionTable();

    /**
     * 去重违规信息表 pledgedata.t_criminalrecords
     *
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    void distinctCriminalRecordTable();

    /**
     * 自动更新股东股份冻结表
     *
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    void autoUpdateHolderShareFrozenTable();

    /**
     * 更新全市场单一股票质押比例
     *
     * <AUTHOR> Ye
     * @date 2025/03/10
     */
    void updateStockMarketPledgeRatio();

    /**
     * 更新全市场单一股票担保物比例
     *
     * <AUTHOR> Ye
     * @date 2025/03/10
     */
    void updateStockMarketCollateralRatio();

}
