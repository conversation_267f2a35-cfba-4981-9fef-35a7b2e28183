package com.tjsj.modify.modules.peer.model.dto;

import com.tjsj.common.enums.basic.SecTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * PeerDataFixDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/1/1 19:30
 * @description 同业数据表修复DTO
 */
@Data
@Accessors(chain = true)
@Alias(value = "PeerDataFixDTO")
@Schema(description = "同业数据表修复DTO")
public class PeerDataFixDTO {

    /**
     * 证券代码
     */
    @Schema(description = "证券代码")
    private String secCode;


    /**
     * 证券名称
     */
    @Schema(description = "证券名称")
    private String secName;

    /**
     * 处理后的证券名称
     */
    @Schema(description = "处理后的证券名称")
    private String processedSecName;


    /**
     * 交易所证券名称
     */
    @Schema(description = "交易所证券名称")
    private String exchangeSecName;

    /**
     * 交易所证券名称2
     */
    @Schema(description = "交易所证券名称2")
    private String exchangeSecName2;

    /**
     * 市场类型
     */
    @Schema(description = "市场类型")
    private String market;

    /**
     * 证券类型
     */
    @Schema(description = "证券类型")
    private SecTypeEnum secType;

    /**
     * 基金简称
     */
    @Schema(description = "基金简称")
    private String fundName;

    /**
     * 基金全称
     */
    @Schema(description = "基金全称")
    private String fundFullName;






}
