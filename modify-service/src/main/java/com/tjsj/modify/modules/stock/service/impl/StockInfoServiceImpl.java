package com.tjsj.modify.modules.stock.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modify.modules.stock.mapper.StockInfoMapper;
import com.tjsj.modify.modules.stock.model.StockInfo;
import com.tjsj.modify.modules.stock.service.StockInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * StockInfoServiceImpl
 *
 * <AUTHOR>
 * @date 2024/07/13
 * @description 股票信息服务实现类
 */
@Service
@RequiredArgsConstructor
public class StockInfoServiceImpl extends ServiceImpl<StockInfoMapper, StockInfo>
        implements StockInfoService {

}

