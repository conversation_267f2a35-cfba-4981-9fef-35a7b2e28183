package com.tjsj.modify.modules.stock.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * HolderShareFrozenLabel
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description 股东股份冻结标签
 * @date 2024/12/12 16:55
 */
@Data
@Accessors(chain = true)
@Alias(value = "HolderShareFrozenLabelDO")
@TableName(value = "labels.t_holder_share_frozen_label")
@Schema(description = "股东股份冻结标签")
public class HolderShareFrozenLabelDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    /**
     * 证券名称
     */
    @TableField(value = "sec_name")
    @Schema(description = "证券名称")
    private String secName;

    /**
     * 标签名称
     */
    @TableField(value = "label_name")
    @Schema(description = "标签名称")
    private String labelName;

    /**
     * 日期
     */
    @TableField(value = "date")
    @Schema(description = "日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 冻结股东名称
     */
    @TableField(value = "frozen_person")
    @Schema(description = "冻结股东名称")
    private String frozenPerson;

    /**
     * 冻结详情
     */
    @TableField(value = "info_describe")
    @Schema(description = "冻结详情")
    private String infoDescribe;

    /**
     * 冻结股份数量
     */
    @TableField(exist = false)
    @Schema(description = "冻结股份数量")
    private Long frozenNumber;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}