package com.tjsj.modify.modules.peer.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * PeerSecCountHistoryDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description 同业券商证券数量统计汇总历史
 * @date 2025/2/10 21:06
 */
@Schema(description = "同业券商证券数量统计汇总历史")
@Data
@Accessors(chain = true)
@Alias(value = "PeerSecCountHistoryDO")
@TableName(value = "margin.t_peer_sec_count_history")
public class PeerSecCountHistoryDO implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 券商名称
     */
    @TableField(value = "`source`")
    @Schema(description = "券商名称")
    private String source;

    /**
     * 日期
     */
    @TableField(value = "`date`")
    @Schema(description = "日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 担保证券数量
     */
    @TableField(value = "collateral_count")
    @Schema(description = "担保证券数量")
    private Integer collateralCount;

    /**
     * 融资标的数量
     */
    @TableField(value = "finance_target_count")
    @Schema(description = "融资标的数量")
    private Integer financeTargetCount;

    /**
     * 融券标的数量
     */
    @TableField(value = "short_sell_target_count")
    @Schema(description = "融券标的数量")
    private Integer shortSellTargetCount;

    /**
     * 集中度分组数量
     */
    @TableField(value = "concentra_group_count")
    @Schema(description = "集中度分组数量")
    private Integer concentraGroupCount;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}