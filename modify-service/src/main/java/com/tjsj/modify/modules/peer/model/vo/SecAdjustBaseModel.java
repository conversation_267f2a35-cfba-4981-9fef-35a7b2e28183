package com.tjsj.modify.modules.peer.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.fastjson2.annotation.JSONField;
import com.tjsj.common.enums.basic.SecTypeEnum;
import com.tjsj.config.converter.enums.SecTypeConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * SecAdjustBaseModel
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @date 2024/12/20 10:15
 * @description 同业券商-证券调整基础模型
 */
@Data
@Accessors(chain = true)
@Alias(value = "SecAdjustBaseModel")
@Schema(description = "同业券商-证券调整基础模型")
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class SecAdjustBaseModel {

    /**
     * 证券代码
     */
    @JSONField(ordinal = 1)
    @Schema(description = "证券代码")
    @ExcelProperty(value = {"证券代码"}, order = 10)
    public String secCode;

    /**
     * 证券名称
     */
    @JSONField(ordinal = 2)
    @Schema(description = "证券名称")
    @ExcelProperty(value = {"证券名称"}, order = 20)
    public String secName;


    /**
     * 证券类型
     */
    @JSONField(ordinal = 3)
    @Schema(description = "证券类型")
    @ExcelProperty(value = "证券类型", converter = SecTypeConverter.class, order = 30)
    public SecTypeEnum secType;

    /**
     * 系统评级
     */
    @JSONField(ordinal = 4)
    @Schema(description = "系统评级")
    @ExcelProperty(value = {"系统评级"}, order = 40)
    public String level;

    /**
     * 调整日期
     */
    @JSONField(ordinal = 5)
    @Schema(description = "调整日期")
    @ExcelProperty(value = {"调整日期"}, order = 50)
    public String date;

    /**
     * 调整类型
     */
    @JSONField(serialize = false)
    @Schema(description = "调整类型")
    @ExcelIgnore
    public String adjustType;

    /**
     * 调整数量
     */
    @JSONField(serialize = false)
    @Schema(description = "调整数量")
    @ExcelIgnore
    public String adjustNum;


}
