package com.tjsj.modify.modules.margin.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdSecDO;

/**
 * ExchangeRuleThresholdSecService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/02/21
 * @description 交易所规则阈值证券服务
 */
public interface ExchangeRuleThresholdSecService extends IService<ExchangeRuleThresholdSecDO>{


    int insertOrUpdate(ExchangeRuleThresholdSecDO record);

    int insertOrUpdateSelective(ExchangeRuleThresholdSecDO record);

    /**
     * 自动更新交易所外规阈值对应的证券具体值
     *
     * <AUTHOR>
     * @date 2025/02/21
     */
    void autoUpdateExchangeRuleThresholdSecData();

}
