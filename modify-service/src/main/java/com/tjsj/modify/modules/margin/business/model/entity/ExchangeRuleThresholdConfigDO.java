package com.tjsj.modify.modules.margin.business.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.basic.SecTypeEnum;
import com.tjsj.modify.modules.common.enums.MrgTrdDataType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ExchangeRuleThresholdConfig
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/02/19
 * @description 交易所外规对于业务参数的阈值规约配置
 */
@Schema(description = "交易所外规对于业务参数的阈值规约配置")
@Data
@Alias(value = "ExchangeRuleThresholdConfigDO")
@Accessors(chain = true)
@TableName(value = "margin.t_exchange_rule_threshold_config")
public class ExchangeRuleThresholdConfigDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 融资融券业务类型
     */
    @TableField(value = "mrd_trd_type")
    @Schema(description = "融资融券业务类型")
    @JSONField(ordinal = 100)
    private MrgTrdDataType mrdTrdType;

    /**
     * 类型名称
     */
    @TableField(value = "type_name")
    @Schema(description = "类型名称")
    @JSONField(ordinal = 200)
    private String typeName;

    /**
     * 证券类型
     */
    @TableField(value = "sec_type")
    @Schema(description = "证券类型")
    @JSONField(ordinal = 300)
    private SecTypeEnum secType;

    /**
     * 阈值
     */
    @TableField(value = "threshold_value")
    @Schema(description = "阈值")
    @JSONField(ordinal = 400)
    private BigDecimal thresholdValue;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    @JSONField(serialize = false)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    @JSONField(serialize = false)
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}