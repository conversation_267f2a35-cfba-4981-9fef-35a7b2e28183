package com.tjsj.modify.modules.market.model.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ExchangeStaticPeSecAdjustHis
 *
 * <AUTHOR>
 * @date 2025/06/11
 * @version 1.0.0
 * @description 交易所静态市盈率规则折算率调整历史记录表
 */
@Schema(description = "交易所静态市盈率规则折算率调整历史记录表")
@Data
@Accessors(chain = true)
@Alias(value = "ExchangeStaticPeSecAdjustHisDO")
@TableName(value = "tj_middle_ground.t_exchange_static_pe_sec_adjust_his")
@ColumnWidth(15)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ExchangeStaticPeSecAdjustHisDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    @JSONField(serialize = false)
    @ExcelIgnore
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    @JSONField(ordinal = 100)
    @ExcelProperty(value = "证券代码", order = 100)
    private String secCode;

    /**
     * 证券名称
     */
    @TableField(value = "sec_name")
    @Schema(description = "证券名称")
    @JSONField(ordinal = 200)
    @ExcelProperty(value = "证券名称", order = 200)
    private String secName;

    /**
     * 变动类型：下调、恢复
     */
    @TableField(value = "adjust_type")
    @Schema(description = "变动类型：下调、恢复")
    @JSONField(ordinal = 250)
    @ExcelProperty(value = "变动类型", order = 250)
    private String adjustType;

    /**
     * 交易日期对应静态市盈率
     */
    @TableField(value = "static_pe")
    @Schema(description = "交易日期对应静态市盈率")
    @JSONField(ordinal = 300)
    @ExcelProperty(value = "交易日期对应静态市盈率", order = 300)
    private BigDecimal staticPe;

    /**
     * 上周最后一个交易日静态市盈率
     */
    @TableField(value = "last_week_last_trading_day_static_pe")
    @Schema(description = "上周最后一个交易日静态市盈率")
    @JSONField(ordinal = 400)
    @ExcelProperty(value = "上周最后一个交易日静态市盈率", order = 400)
    private BigDecimal lastWeekLastTradingDayStaticPe;

    /**
     * 交易日期
     */
    @TableField(value = "`date`")
    @Schema(description = "交易日期")
    @JSONField(ordinal = 500, format = "yyyy-MM-dd")
    @ExcelProperty(value = "交易日期", order = 500)
    private LocalDate date;

    @TableField(value = "create_time")
    @Schema(description = "")
    @JSONField(serialize = false)
    @ExcelIgnore
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description = "")
    @JSONField(serialize = false)
    @ExcelIgnore
    private LocalDateTime updateTime;

}