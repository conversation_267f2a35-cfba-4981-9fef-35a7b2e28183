package com.tjsj.modify.modules.peer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.constants.ManageConsts;
import com.tjsj.common.enums.basic.CommonStatusEnum;
import com.tjsj.common.enums.basic.SecTypeEnum;
import com.tjsj.common.utils.data.ParallelExecutorUtil;
import com.tjsj.common.utils.data.UpdateRealUtil;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import com.tjsj.modify.modules.peer.mapper.PeerDataMapper;
import com.tjsj.modify.modules.peer.model.dto.PeerDataFixDTO;
import com.tjsj.modify.modules.peer.model.dto.PeerSecuritiesColumn;
import com.tjsj.modify.modules.peer.model.entity.PeerSecuritiesSetting;
import com.tjsj.modify.modules.peer.model.entity.PeerTableDataFixLogDO;
import com.tjsj.modify.modules.peer.service.PeerSecuritiesSettingService;
import com.tjsj.modify.modules.peer.service.PeerTableDataFixLogService;
import com.tjsj.modules.base.service.TradingDayService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.PessimisticLockingFailureException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PeerDataService
 *
 * <AUTHOR> Ye
 * @date 2024/07/19
 * @description 同业数据服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Schema(name = "PeerDataService", description = "同业数据服务")
public class PeerDataService {
    @Resource
    private PeerTableDataFixLogService peerTableDataFixLogService;

    private final PeerSecuritiesSettingService peerSecuritiesSettingService;

    private final PeerDataMapper peerDataMapper;

    private final UpdateRealUtil updateRealUtil;

    private final TradingDayService tradingDayService;

    /**
     * 修复同业表数据
     *
     * <AUTHOR> Ye
     * @date 2025/01/01
     */
    public void fixPeerTableData() {


        // 给同业券商表结构添加 enable_status 字段
        updateRealUtil.executeUpdateMethodOneParam(this::addPeerTableDesignateField,
                PeerConsts.TABLE_FIELD_ENABLE_STATUS);
        // 给同业券商表结构添加 if_market_checked 字段
        updateRealUtil.executeUpdateMethodOneParam(this::addPeerTableDesignateField,
                PeerConsts.TABLE_FIELD_IF_MARKET_CHECKED);
        // 修复同业表市场字段为空的数据
        updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldNullValue, "fixPeerTableMarketFieldNullValue");
        // 修复同业券商的market字段值错误的记录
        updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldErrorRecord,
                "fixPeerTableMarketFieldErrorRecord");
    }

    /**
     * 并行处理同业表市场字段为空的数据
     */
    public void fixPeerTableMarketFieldNullValue() {
        String schemaName = ManageConsts.SCHEMA_CREDIT;

        // 获取需要处理的表列表
        List<String> marketFieldNullValuePeerTableNameList = peerSecuritiesSettingService
                .getPeerAgencyInfoList(true)
                .stream()
                .map(PeerSecuritiesSetting::getMarketFieldUpdateTable)
                .filter(Objects::nonNull)
                .flatMap(tableNameStr -> Arrays.stream(tableNameStr.split(";"))
                        .map(String::trim))
                .toList();
        if (CollUtil.isEmpty(marketFieldNullValuePeerTableNameList)) {
            return;
        }

        // 根据需要决定线程池大小
        int poolSize = ParallelExecutorUtil.defaultPoolSize();

        // 构建Runnable任务列表
        List<Runnable> tasks = new ArrayList<>();
        for (String peerTableName : marketFieldNullValuePeerTableNameList) {

            tasks.add(() -> {
                try {

                    List<PeerDataFixDTO> marketFieldNullValueData =
                            peerDataMapper.getPeerTableMarketFieldNullValueData(peerTableName, schemaName);
                    if (CollUtil.isNotEmpty(marketFieldNullValueData)) {
                        // 1.修复 通过证券名称字符重合度
                        this.fixPeerTableMarketFieldBySecNameCharOverlap(peerTableName, schemaName,
                                marketFieldNullValueData, null);
                        // 2.修复 通过基金方法
                        this.updateMarketFieldByFundMethod(peerTableName, schemaName);
                    }

                } catch (Exception e) {
                    log.error("修复表 {} 的Market字段时出现异常", peerTableName, e);
                }
            });
        }

        // 调用公共方法并行执行
        ParallelExecutorUtil.executeTasks(tasks, poolSize, true);

        // 任务结束
        log.info("FixPeerTableMarketFieldNullValue completed. total tables: {}",
                marketFieldNullValuePeerTableNameList.size());
    }


    /**
     * 修复同业表Market字段通过证券名称字符重合度
     *
     * @param tableName   表名称
     * @param schemaName  架构名称
     * @param marketFieldNullValueData 市场字段为空数据
     * @param maxDateTime 最大更新时间
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    private void fixPeerTableMarketFieldBySecNameCharOverlap(String tableName, String schemaName,
                                                             List<PeerDataFixDTO> marketFieldNullValueData,
                                                             LocalDateTime maxDateTime) {
        // 使用 Map 记录重复的 secCode 和 secName 组合
        Map<String, List<PeerDataFixDTO>> duplicateRecords = new HashMap<>();

        // 按 secCode 和 secName 组合记录分组
        marketFieldNullValueData.forEach(secInfo -> {

            String key = secInfo.getSecCode() + ":" + secInfo.getSecName();
            duplicateRecords.computeIfAbsent(key, k -> new ArrayList<>()).add(secInfo);
        });

        // 遍历分组后的记录
        duplicateRecords.forEach((key, records) -> {

            if (records.size() > 1) {

                // 如果有重复记录，代表当前证券代码在两家交易所都有，需要选择其中一个，找到重合字符最多的记录
                PeerDataFixDTO bestMatchRecord = this.findBestMatchRecord(records);
                if (bestMatchRecord == null) {
                    return;
                }

                // 更新市场字段
                peerDataMapper.updateMarketField(tableName, schemaName, bestMatchRecord, maxDateTime);
            } else {

                // 只有一条记录
                PeerDataFixDTO onlyThisRecord = records.get(0);
                SecTypeEnum secType = onlyThisRecord.getSecType();
                String secName = onlyThisRecord.getProcessedSecName();
                String jysSecName = onlyThisRecord.getExchangeSecName();
                int overlapCharCount = calculateStringOverlap(secName, jysSecName);
                if (overlapCharCount <= 1) {
                    // 重合字符数量为 1，证明不是同一证券，不更新
                    return;
                }

                // 如果secName和jysSecName其中一个的字符中包含"国债"或者"贴债"，而另一个不包含，则判断为不是同一证券
                List<String> keywords = List.of("国债", "贴债");
                for (String keyword : keywords) {
                    boolean secNameContains = secName.contains(keyword);
                    boolean jysSecNameContains = jysSecName.contains(keyword);
                    if (secNameContains != jysSecNameContains && secType.equals(SecTypeEnum.BOND)) {
                        if (StrUtil.isNotEmpty(secName) && StrUtil.isNotEmpty(jysSecName)) {
                            for (char jysSecNameChar : jysSecName.toCharArray()) {
                                if (!secName.contains(String.valueOf(jysSecNameChar))) {
                                    for (char secNameChar : secName.toCharArray()) {
                                        if (!jysSecName.contains(String.valueOf(secNameChar))) {
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                peerDataMapper.updateMarketField(tableName, schemaName, onlyThisRecord, maxDateTime);
            }
        });

    }


    /**
     * 找到 jysSecName 与 secName 字符重合最多的记录
     *
     * @param records 记录列表
     * @return 重合最多的记录
     */
    private PeerDataFixDTO findBestMatchRecord(List<PeerDataFixDTO> records) {

        return records.stream()
                .max((record1, record2) -> {
                    String secName = record1.getSecName();
                    String jysSecName1 = record1.getExchangeSecName();
                    String jysSecName2 = record2.getExchangeSecName();

                    int overlap1 = calculateStringOverlap(secName, jysSecName1);
                    int overlap2 = calculateStringOverlap(secName, jysSecName2);

                    // TODO:
                    return Integer.compare(overlap1, overlap2);
                })
                .orElse(records.get(0));
    }

    /**
     * 计算两个字符串的字符重合数量
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @return 重合数量
     */
    public static int calculateStringOverlap(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0;
        }

        Set<Character> set1 = str1.chars().mapToObj(c -> (char) c).collect(Collectors.toSet());
        Set<Character> set2 = str2.chars().mapToObj(c -> (char) c).collect(Collectors.toSet());

        set1.retainAll(set2);
        return set1.size();
    }

    public static void main(String[] args) {
        int i = calculateStringOverlap("嘉实300A", "沪深300LOF");
        int i2 = calculateStringOverlap("嘉实300A", "20天津38");
        log.error(i + " " + i2);
    }


    /**
     * 更新市场字段通过基金方法
     *
     * @param tableName  同业名称
     * @param schemaName 数据库名称
     * <AUTHOR>
     * @date 2025/01/01
     */
    public void updateMarketFieldByFundMethod(String tableName, String schemaName) {

        List<PeerDataFixDTO> peerDataFixDTOList = peerDataMapper.getPeerTableMarketFieldNonDataByFundMethod(tableName,
                schemaName);

        this.updateMarketFieldByFundMethod(tableName, schemaName, peerDataFixDTOList);
    }

    /**
     * 更新市场字段通过基金方法
     *
     * @param peerName 同业名称
     * @param schemaName 数据库名称
     * @param peerDataFixDTOList 证券信息列表
     *
     * <AUTHOR>
     * @date 2025/01/01
     */
    private void updateMarketFieldByFundMethod(String peerName, String schemaName,
                                               List<PeerDataFixDTO> peerDataFixDTOList) {

        peerDataFixDTOList.stream()
                .filter(secInfo ->
                        this.isFundNameFullyContained(secInfo.getProcessedSecName(), secInfo.getFundFullName(),
                                secInfo.getFundName(), secInfo.getExchangeSecName(), secInfo.getExchangeSecName2()))
                .forEach(secInfo -> peerDataMapper.updateMarketField(peerName, schemaName, secInfo, null));

    }


    /**
     * 检查基金名称是否完全包含证券名称的所有字符
     *
     * @param secName          证券名称
     * @param fundFullName     基金全称
     * @param fundName         基金名称
     * @param exchangeSecName  交易所证券名称
     * @param exchangeSecName2 交易所证券名称2
     * @return true 如果基金全称或基金名称包含所有证券名称字符
     */
    private boolean isFundNameFullyContained(String secName, String fundFullName, String fundName,
                                             String exchangeSecName, String exchangeSecName2) {
        //
        for (char c : secName.toCharArray()) {
            if (!fundFullName.contains(String.valueOf(c)) && !fundName.contains(String.valueOf(c))) {
                if (!exchangeSecName.contains(String.valueOf(c)) && !exchangeSecName2.contains(String.valueOf(c))) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 给同业券商表添加指定字段
     *
     * @param tableFieldName 表字段名称
     * <AUTHOR> Ye
     * @date 2025/01/01
     */
    public void addPeerTableDesignateField(String tableFieldName) {

        //筛选出所有缺少指定字段的同业券商数据表
        //使用DDL语句修改表结构，增加指定字段字段
        peerDataMapper.selectPeerTableWithoutDesignateField(ManageConsts.SCHEMA_CREDIT, tableFieldName)
                .forEach(tableName -> {

                    if (PeerConsts.TABLE_FIELD_ENABLE_STATUS.equals(tableFieldName)) {

                        peerDataMapper.addPeerTableEnableStatusField(tableName, ManageConsts.SCHEMA_CREDIT,
                                tableFieldName);
                    } else if (PeerConsts.TABLE_FIELD_IF_MARKET_CHECKED.equals(tableFieldName)) {

                        peerDataMapper.addPeerTableIfMarketCheckedField(tableName, ManageConsts.SCHEMA_CREDIT,
                                tableFieldName);
                    }

                });
    }


    /**
     * 更新同业表market字段
     *
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    public void fixPeerTableMarketFieldErrorRecord() {

        // 获取有效的同业券商的所有表名称列表
        List<String> tableNameList = peerSecuritiesSettingService.getValidPeerSecuritiesTableNameList();

        List<String> marketFieldNullValuePeerTableNameList = peerSecuritiesSettingService.getPeerAgencyInfoList(true)
                .stream()
                .map(PeerSecuritiesSetting::getMarketFieldUpdateTable)
                .filter(Objects::nonNull)
                .flatMap(tableNameStr -> Arrays.stream(tableNameStr.split(";"))
                        .map(String::trim))
                .toList();


        List<Runnable> tasks = new ArrayList<>();
        tableNameList.forEach(tableName -> {
            // 以 表为更新单位，并行执行
            tasks.add(() -> updateRealUtil.executeUpdateMethod(this::fixPeerTableMarketFieldErrorRecord
                    , tableName, marketFieldNullValuePeerTableNameList, "fixPeerTableMarketFieldErrorRecord"));

        });

        // 并行执行任务
        ParallelExecutorUtil.executeTasks(tasks, null, true);
        // 将credit.t_peer_mrg_trd_market_temp表所有数据清除
        this.peerDataMapper.truncatePeerMrgTrdMarketTempTable();

    }

    /**
     * 修复同业表market字段错误记录
     *
     * @param tableName 表名称
     * @param marketFieldNullValuePeerTableNameList 市场字段为空的同业券商表名称列表
     *
     * <AUTHOR> Ye
     * @date 2025/06/13
     */
    public void fixPeerTableMarketFieldErrorRecord(String tableName,
                                                   List<String> marketFieldNullValuePeerTableNameList) {

        Boolean ifThisTableMarketFieldNullValueTable = marketFieldNullValuePeerTableNameList.contains(tableName);

        // 先将存在重复的记录置为有问题的记录
        this.peerDataMapper.updateDuplicateRecordIfMarketCheckedToOne(tableName);
        // 获取同业券商表market字段未检查过的记录数量
        Integer nonCheckRecordCount = peerDataMapper.getPeerTableMarketFieldNonCheckRecordCount(tableName);

        if (nonCheckRecordCount > 0) {

            LocalDateTime startTime = LocalDateTime.now();

            // 获取当前表的最大截取时间戳
            LocalDateTime maxDateTime = this.peerDataMapper.selectPeerTableMaxUpdateTime(tableName);
            if (maxDateTime == null) {
                return;
            }

            try {
                // 将有问题的记录插入问题表credit.t_rzrq_market_error_record中
                this.peerDataMapper.insertStockTypeWrongRecordToErrorTable(tableName, maxDateTime,
                        ifThisTableMarketFieldNullValueTable);

                // 1.股票类型记录，根据股票对应的交易所修复market字段
                // this.peerDataMapper.fixMarketFieldErrorValueByStockType(tableName, maxDateTime);
                this.peerDataMapper.fixMarketFieldErrorValueByStockTypeNew(tableName, maxDateTime);

                // 2.修复基金、债券类型的记录的market 字段
                // 方法零：根据匹配上的过去日期的记录进行更新
                this.peerDataMapper.updateMarketFieldByPastDateMatchedRecord(tableName, maxDateTime);

                // 方法一：通过证券名称的字符重合度
                // 将 证券名称 能模糊匹配上的基金或债券类型记录更新 market 字段
                // this.peerDataMapper.updateMatchedFundOrBondTypeMarketField(tableName, maxDateTime);
                this.peerDataMapper.updateMatchedFundOrBondTypeMarketFieldNew(tableName, maxDateTime);
                this.peerDataMapper.updateMatchedFundOrBondTypeMarketField2(tableName, maxDateTime);

                // 更新同业券商表的market字段
                this.peerDataMapper.updateTableMarketField(tableName);

                List<PeerDataFixDTO> marketFieldNonCheckedData = peerDataMapper.getPeerTableMarketFieldNonCheckedData(
                        tableName, ManageConsts.SCHEMA_CREDIT, maxDateTime);
                this.fixPeerTableMarketFieldBySecNameCharOverlap(tableName, ManageConsts.SCHEMA_CREDIT,
                        marketFieldNonCheckedData, maxDateTime);

                // 方法二：通过基金信息表的信息
                List<PeerDataFixDTO> fundInfoList = peerDataMapper.getPeerTableMarketFieldNonCheckedByFundMethod(
                        tableName, ManageConsts.SCHEMA_CREDIT, maxDateTime);
                this.updateMarketFieldByFundMethod(tableName, ManageConsts.SCHEMA_CREDIT, fundInfoList);

                // 方法三：时间判断
                // 将几个月前的记录且在交易所证券信息表中找不到的记录，即代表已经退市或结束的证券的enable_status置为1,if_market_checked置为0
                this.peerDataMapper.updateDeListSecurityEnableStatusToDisable(tableName, maxDateTime);
                // 将经过以上程序仍然没有匹配到对应交易所的记录的if_market_checked置为2，即表示虽然经过了处理，但仍然没有匹配到交易所
                this.peerDataMapper.updateUnmatchedSecurityIfMarketCheckedTo2(tableName, maxDateTime);

                // 保存修复日志
                this.saveFixLog(tableName, startTime);

            } catch (PessimisticLockingFailureException pessimisticLockingFailureException) {

                log.error("更新同业表 {} 的Market字段时出现异常", tableName, pessimisticLockingFailureException);
            }

        }

    }

    /**
     * 保存修复日志
     *
     * @param tableName 表名称
     * @param startTime 开始时间
     * <AUTHOR> Ye
     * @date 2025/06/13
     */
    private void saveFixLog(String tableName, LocalDateTime startTime) {

        LocalDateTime endTime = LocalDateTime.now();
        PeerTableDataFixLogDO peerTableDataFixLog =
                new PeerTableDataFixLogDO().setTableName(tableName).setStartTime(startTime).setEndTime(endTime)
                        .setDate(LocalDate.now())
                        .setTimeDuration(Duration.between(startTime, endTime).toMillis());

        // 保持本次表修复日志
        peerTableDataFixLogService.save(peerTableDataFixLog);
    }

    /**
     * 更新同业证券数据
     * <p>
     * 更新margin.t_peer_sec_category_data表中的数据
     * 更新margin.t_peer_sec_collateral_data表中的数据
     * 更新margin.t_peer_sec_finance_target_data表中的数据
     * 更新margin.t_peer_sec_short_sell_target_data表中的数据
     * </p>
     *
     * <AUTHOR> Ye
     * @date 2025/02/04
     */
    public void updatePeerSecData() {

        this.updatePeerSecData(null);
    }

    /**
     * 更新同业证券数据
     * <p>更新指定天数的同业证券数据</p>
     *
     * @param days 天
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    public void updatePeerSecData(Integer days) {

        // 获取当前日期
        String today = DateUtil.today();
        // 获取 n 天前的日期
        days = days == null ? -3 : days;
        String thirtyDaysAgo = DateUtil.offsetDay(DateUtil.date(), days).toDateStr();
        // 获取交易日期列表
        List<String> tradingDateList = tradingDayService.queryDateRangeTradingDays(thirtyDaysAgo, today);

        this.updateDateListPeerSecData(tradingDateList);
    }

    /**
     * 更新同业证券数据
     * <p>根据开始日期和结束日期更新同业证券数据</p>
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    public void updatePeerSecData(String startDate, String endDate) {

        List<String> tradingDateList = tradingDayService.queryDateRangeTradingDays(startDate, endDate);

        this.updateDateListPeerSecData(tradingDateList);
    }

    /**
     * 更新同业证券数据
     *
     * @param tradingDateList 交易日期列表
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    private void updateDateListPeerSecData(List<String> tradingDateList) {

        List<PeerSecuritiesSetting> peerSecuritiesSettings =
                peerSecuritiesSettingService.list(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                        .eq(PeerSecuritiesSetting::getIfColumnShow, CommonStatusEnum.ENABLE));

//		peerSecuritiesSettings = peerSecuritiesSettings.stream().filter(peerSecuritiesSetting ->
//						peerSecuritiesSetting.getEnName().contains("cczq"))
//				.toList();

        // 遍历日期列表，提交任务到线程池
        for (String tradingDate : tradingDateList) {

            this.updateTradingDatePeerSecData(tradingDate, peerSecuritiesSettings);
        }

    }

    /**
     * 更新指定交易日期同业证券数据
     *
     * @param tradingDate           交易日期
     * @param peerSecuritiesConfigs 同业券商配置信息
     * <AUTHOR> Ye
     * @date 2025/02/04
     */
    public void updateTradingDatePeerSecData(String tradingDate, List<PeerSecuritiesSetting> peerSecuritiesConfigs) {

        List<Runnable> tasks = new ArrayList<>();
        peerSecuritiesConfigs.forEach(peerSecuritiesConfig -> {

            tasks.add(() -> {

                String peerEnName = peerSecuritiesConfig.getEnName();
                String collateralTableName =
                        ManageConsts.SCHEMA_CREDIT + "." + PeerConsts.PEER_PREFIX + peerEnName + PeerConsts.COLLATERAL_SUFFIX;
                String underlyingSecuritiesTableName =
                        ManageConsts.SCHEMA_CREDIT + "." + PeerConsts.PEER_PREFIX + peerEnName + PeerConsts.UNDERLYING_SUFFIX;
                String categoryTableName =
                        ManageConsts.SCHEMA_CREDIT + "." + PeerConsts.PEER_PREFIX + peerEnName + PeerConsts.CONCENTRATION_SUFFIX;


                // 更新margin.t_peer_sec_collateral_data表中的 同业担保品 数据
                synchronized (PeerConsts.COLLATERAL_SUFFIX) {
                    peerDataMapper.insertOrUpdatePeerHaircutData(peerEnName, collateralTableName, tradingDate);
                }
                synchronized (PeerConsts.UNDERLYING_SUFFIX) {
                    // 更新margin.t_peer_sec_finance_target_data表中的 同业融资标的 数据
                    peerDataMapper.insertOrUpdatePeerFinanceTargetData(peerEnName, underlyingSecuritiesTableName,
                            tradingDate);
                    // 更新margin.t_peer_sec_short_sell_target_data表中的 同业融券标的 数据
                    peerDataMapper.insertOrUpdatePeerShortSellTargetData(peerEnName, underlyingSecuritiesTableName,
                            tradingDate);
                }
                synchronized (PeerConsts.CONCENTRATION_SUFFIX) {
                    // 更新margin.t_peer_sec_category_data表中的 同业集中度分组 数据
                    try {
                        peerDataMapper.insertOrUpdatePeerCategoryData(peerEnName, categoryTableName, tradingDate);
                    } catch (Exception ignored) {
                    }
                }

            });

        });

        ParallelExecutorUtil.executeTasks(tasks, null, true);

        // 将不存在集中度分组的同业券商的t_peer_sec_category_data表中的值进行更新
        this.updatePeerSecuritiesNoCategory(tradingDate);

        // 更新所有证券的1.平均值avg_value字段；2.纳入范围的券商数量include_peer_count字段
        peerDataMapper.updatePeerAverageHaircutData(tradingDate);
        peerDataMapper.updatePeerAverageFinanceTargetData(tradingDate);
        peerDataMapper.updatePeerAverageShortSellTargetData(tradingDate);
        //peerDataMapper.updatePeerCategoryRange();

    }

    /**
     * 将不存在集中度分组的同业券商的t_peer_sec_category_data表中的值进行更新
     *
     * @param tradingDate 交易日期
     * <AUTHOR> Ye
     * @date 2025/02/11
     */
    private void updatePeerSecuritiesNoCategory(String tradingDate) {
        // 固定的几个没有集中度分组的同业券商
        List<String> categoryNonExistPeerSecurities = Arrays.asList(PeerSecuritiesColumn.Fields.haitongzq,
                PeerSecuritiesColumn.Fields.zszq, PeerSecuritiesColumn.Fields.dfzq,
                PeerSecuritiesColumn.Fields.gjzq, PeerSecuritiesColumn.Fields.dbzq,
                PeerSecuritiesColumn.Fields.dxzq, PeerSecuritiesColumn.Fields.cczq,
                PeerSecuritiesColumn.Fields.hxzq, PeerSecuritiesColumn.Fields.njzq,
                PeerSecuritiesColumn.Fields.pazq, PeerSecuritiesColumn.Fields.xyzq,
                PeerSecuritiesColumn.Fields.gyzq, PeerSecuritiesColumn.Fields.xnzq,
                PeerSecuritiesColumn.Fields.zjgs);
        // 更新不存在集中度分组的同业券商的t_peer_sec_category_data表中的值
        peerDataMapper.updatePeerCategoryDataNonExist(categoryNonExistPeerSecurities, tradingDate);

    }
}
