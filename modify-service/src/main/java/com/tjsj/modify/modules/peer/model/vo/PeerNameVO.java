package com.tjsj.modify.modules.peer.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/7/4 17:41
 * @description 同业券商 名称信息
 */
@Schema(name = "PeerNameVO", description = "返回的同业券商名称信息")
@Data
@Accessors(chain = true)
public class PeerNameVO {

    /**
     * 中文名称
     */
    @Schema(description = "中文名称")
    private String cnName;

    /**
     * 英文名称
     */
    @Schema(description = "英文名称")
    private String enName;


}
