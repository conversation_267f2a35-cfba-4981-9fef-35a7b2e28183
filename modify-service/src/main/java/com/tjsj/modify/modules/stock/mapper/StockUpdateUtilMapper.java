package com.tjsj.modify.modules.stock.mapper;

import com.tjsj.modify.modules.stock.model.HolderShareFrozenDO;
import com.tjsj.modify.modules.stock.model.HolderShareFrozenLabelDO;
import com.tjsj.modify.modules.stock.model.LatestHolderInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/2/8 16:36
 * @description
 */
@Mapper
public interface StockUpdateUtilMapper {
    /**
     * 去重股东历史表 pledgedata.t_tenshareholderinfos
     *
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    void distinctHolderHistoryTable();

    /**
     * 选择新最新股东列表
     *
     * @param newFilterSecCode 新过滤证券代码
     * @return {@link List }<{@link LatestHolderInfoDO }>
     * <AUTHOR>
     * @date 2025/02/08
     */
    List<LatestHolderInfoDO> selectNewLatestHolderList(@Param("newFilterSecCode") String newFilterSecCode);

    /**
     * 选择新最新股东列表2
     *
     * @param newSecCodeList   新证券代码列表
     * @param newFilterSecCode 新过滤证券代码
     * @return {@link List }<{@link LatestHolderInfoDO }>
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    List<LatestHolderInfoDO> selectNewLatestHolderList2(@Param(value = "newSecCodeList") List<String> newSecCodeList,
                                                        @Param(value = "newFilterSecCode") String newFilterSecCode);

    /**
     * 去重股东减持表 credit.t_dfcf_equity_pledge
     *
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    void distinctHolderReductionTable();

    /**
     * 去重违规信息表 pledgedata.t_criminalrecords
     *
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    void distinctCriminalRecordTable();


    /**
     * 更新 frozenPerson 字段
     *
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    void updateHolderShareFrozenTableFrozenPersonField();

    /**
     * 查询新股东股份冻结列表
     *
     * @return {@link List }<{@link HolderShareFrozenDO }>
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    List<HolderShareFrozenDO> selectNewHolderShareFrozenList();

    /**
     * 列表股东冻结信息
     *
     * @param holderType 股东类型
     * @return {@link List }<{@link HolderShareFrozenLabelDO }>
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    List<HolderShareFrozenLabelDO> listHolderFrozenInfo(@Param("holderType") String holderType);

    /**
     * 获取当前证券的当前股东的股份冻结标签信息
     *
     * @param secCode      证券代码
     * @param frozenPerson 冻结人
     * @param frozenNumber 冻结数
     * @param holderType   股东类型
     * @return {@link List }<{@link HolderShareFrozenLabelDO }>
     * <AUTHOR> Ye
     * @date 2024/12/12
     */
    List<HolderShareFrozenLabelDO> getSecFrozenPersonLabelInfo(@Param("secCode") String secCode,
                                                               @Param("frozenPerson") String frozenPerson,
                                                               @Param("frozenNumber") Long frozenNumber,
                                                               @Param("holderType") String holderType);

    /**
     * 更新全市场单一股票质押比例
     *
     * <AUTHOR> Ye
     * @date 2025/03/10
     */
    void updateStockMarketPledgeRatio();

    /**
     * 更新全市场单一股票担保比例
     *
     * <AUTHOR> Ye
     * @date 2025/03/10
     */
    void updateStockMarketCollateralRatio();

    /**
     * 删除pledgedata.t_frozen表中的重复数据
     *
     * @return int 影响的行数
     */
    int deleteFrozenTableDuplicateData();
}
