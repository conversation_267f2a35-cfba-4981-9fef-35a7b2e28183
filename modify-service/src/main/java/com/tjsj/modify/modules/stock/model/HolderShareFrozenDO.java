package com.tjsj.modify.modules.stock.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.annotation.data.HashExclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * HolderShareFrozen
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description 股东股份冻结详情表
 * @date 2024/12/12 13:54
 */
@Data
@Accessors(chain = true)
@TableName(value = "tj_middle_ground.t_holder_share_frozen")
@Schema(description = "股东股份冻结详情表")
@Alias(value = "HolderShareFrozenDO")
public class HolderShareFrozenDO implements Serializable {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键id")
    @HashExclude
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    /**
     * 冻结股东名称
     */
    @TableField(value = "frozen_person")
    @Schema(description = "冻结股东名称")
    private String frozenPerson;

    /**
     * 冻结股份数量
     */
    @TableField(value = "frozen_number")
    @Schema(description = "冻结股份数量")
    private Long frozenNumber;

    /**
     * 解冻股份数量
     */
    @TableField(value = "unfrozen_number")
    @Schema(description = "解冻股份数量")
    private Long unfrozenNumber;

    /**
     * 公告日期
     */
    @TableField(value = "publish_date")
    @Schema(description = "公告日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate publishDate;

    /**
     * 执行开始日期
     */
    @TableField(value = "start_date")
    @Schema(description = "执行开始日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 执行结束日期
     */
    @TableField(value = "end_date")
    @Schema(description = "执行结束日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 冻结/解除冻结 原因
     */
    @TableField(value = "frozen_reason")
    @Schema(description = "冻结/解除冻结 原因")
    private String frozenReason;

    /**
     * 冻结股份类型id
     */
    @TableField(value = "frozen_share_nature_id")
    @Schema(description = "冻结股份类型id")
    private Integer frozenShareNatureId;

    /**
     * 冻结股份占比比例
     */
    @TableField(value = "frozen_total_ratio")
    @Schema(description = "冻结股份占比比例")
    private BigDecimal frozenTotalRatio;

    /**
     * 冻结申请人
     */
    @TableField(value = "freeze_applicant")
    @Schema(description = "冻结申请人")
    private String freezeApplicant;

    /**
     * 冻结执行人
     */
    @TableField(value = "freeze_executor")
    @Schema(description = "冻结执行人")
    private String freezeExecutor;


    /**
     * 解冻详情
     */
    @TableField(value = "unfrozen_detail")
    @Schema(description = "解冻详情")
    private String unfrozenDetail;


    /**
     * 哈希值，表唯一记录标识
     */
    @TableField(value = "hash")
    @Schema(description = "哈希值，表唯一记录标识")
    private String hash;


    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    @HashExclude
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    @HashExclude
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}