package com.tjsj.modify.modules.market.mapper;

import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecDO;
import com.tjsj.modify.modules.market.model.entity.SecTradingDayDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MarketMapper
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/2/5 19:36
 * @description
 */
@Mapper
public interface MarketUpdateUtilMapper {

	/**
	 * 判断当日是否是本周最后一个交易日，且行情表已经更新完成
	 *
	 * @param ifTodayLastTradingDayThisWeekAndMarketDataUpdated 判断当日是否是本周最后一个交易日，且行情表已经更新完成
	 * @return {@link List }<{@link ExchangeStaticPeSecDO }>
	 * <AUTHOR> Ye
	 * @date 2025/02/05
	 */
	List<ExchangeStaticPeSecDO> selectNewExchangeStaticPeSecData(
			@Param("ifTodayLastTradingDayThisWeekAndMarketDataUpdated")
			boolean ifTodayLastTradingDayThisWeekAndMarketDataUpdated);

	/**
	 * 选择新股票交易日期
	 *
	 * @return {@link List }<{@link SecTradingDayDO }>
	 * <AUTHOR> Ye
	 * @date 2025/02/10
	 */
	List<SecTradingDayDO> selectNewStockTradingDay();

	/**
	 * 选择最早的交易日期
	 *
	 * @return {@link List }<{@link SecTradingDayDO }>
	 * <AUTHOR> Ye
	 * @date 2025/02/10
	 */
	List<SecTradingDayDO> selectMinTradingDay();

	/**
	 * 选择老股票交易日期
	 *
	 * @return {@link List }<{@link SecTradingDayDO }>
	 * <AUTHOR> Ye
	 * @date 2025/02/10
	 */
	List<SecTradingDayDO> selectOldStockTradingDay();

	/**
	 * 选择之前月证券最高交易日期
	 *
	 * @return {@link List }<{@link SecTradingDayDO }>
	 * <AUTHOR> Ye
	 * @date 2025/03/07
	 */
	List<SecTradingDayDO> selectBeforeMonthSecMaxTradingDate();

	/**
	 * 判断当日是否是本周最后一个交易日，且行情表已经更新完成
	 *
	 * @return boolean
	 * <AUTHOR> Ye
	 * @date 2025/03/09
	 */
	Integer checkIfLastTradingDayThisWeekAndMarketDataUpdated();

}
