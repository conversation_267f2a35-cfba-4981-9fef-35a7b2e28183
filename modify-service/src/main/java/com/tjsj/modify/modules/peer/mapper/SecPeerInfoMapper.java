package com.tjsj.modify.modules.peer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.modify.modules.peer.model.entity.SecPeerInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * SecPeerInfoMapper
 *
 * <AUTHOR>
 * @date 2024/8/9 10:35
 * @version 1.0.0
 * @description 同业券商-最新两融业务数据-Mapper
 */
@Mapper
public interface SecPeerInfoMapper extends BaseMapper<SecPeerInfoDO> {


    /**
     * 更新证券同业信息担保品
     *
     * @param tableName 表名称
     * @param cnName cn名称
     *
     * <AUTHOR>
     * @date 2025/06/06
     */
    @CheckCount(count = 1)
    void updateSecPeerInfoCollateral(@Param("tableName") String tableName, @Param("cnName") String cnName);

}