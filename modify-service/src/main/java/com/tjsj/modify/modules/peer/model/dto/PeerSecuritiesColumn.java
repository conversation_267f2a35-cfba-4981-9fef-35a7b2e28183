package com.tjsj.modify.modules.peer.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

/**
 * PeerSecuritiesColumn
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/20 15:56
 * @description 同业券商表格列
 */
@Data
@Accessors(chain = true)
@Alias(value = "PeerSecuritiesColumn")
@FieldNameConstants
@Schema(description = "同业券商表格列")
public class PeerSecuritiesColumn {

    /**
     * 中信证券
     */
    @TableField(value = "zxzq")
    @Schema(description = "中信证券")
    @ExcelProperty(value = {"中信证券"})
    @JSONField(ordinal = 100)
    private String zxzq;

    /**
     * 银河证券
     */
    @TableField(value = "zgyh")
    @Schema(description = "银河证券")
    @ExcelProperty(value = {"银河证券"})
    @JSONField(ordinal = 110)
    private String zgyh;

    /**
     * 国泰君安
     */
    @TableField(value = "gtja")
    @Schema(description = "国泰君安")
    @ExcelProperty(value = {"国泰君安"})
    @JSONField(ordinal = 120)
    private String gtja;

    /**
     * 华泰证券
     */
    @TableField(value = "htzq")
    @Schema(description = "华泰证券")
    @ExcelProperty(value = {"华泰证券"})
    @JSONField(ordinal = 130)
    private String htzq;

    /**
     * 中信建投
     */
    @TableField(value = "zxjt")
    @Schema(description = "中信建投")
    @ExcelProperty(value = {"中信建投"})
    @JSONField(ordinal = 140)
    private String zxjt;

    /**
     * 中金公司
     */
    @TableField(value = "zjgs")
    @Schema(description = "中金公司")
    @ExcelProperty(value = {"中金公司"})
    @JSONField(ordinal = 150)
    private String zjgs;

    /**
     * 海通证券
     */
    @TableField(value = "haitongzq")
    @Schema(description = "海通证券")
    @ExcelProperty(value = {"海通证券"})
    @JSONField(ordinal = 160)
    private String haitongzq;

    /**
     * 申万宏源
     */
    @TableField(value = "swhy")
    @Schema(description = "申万宏源")
    @ExcelProperty(value = {"申万宏源"})
    @JSONField(ordinal = 170)
    private String swhy;

    /**
     * 广发证券
     */
    @TableField(value = "gfzq")
    @Schema(description = "广发证券")
    @ExcelProperty(value = {"广发证券"})
    @JSONField(ordinal = 180)
    private String gfzq;

    /**
     * 招商证券
     */
    @TableField(value = "zszq")
    @Schema(description = "招商证券")
    @ExcelProperty(value = {"招商证券"})
    @JSONField(ordinal = 190)
    private String zszq;

    /**
     * 国信证券
     */
    @TableField(value = "gxzq")
    @Schema(description = "国信证券")
    @ExcelProperty(value = {"国信证券"})
    @JSONField(ordinal = 200)
    private String gxzq;

    /**
     * 东方证券
     */
    @TableField(value = "dfzq")
    @Schema(description = "东方证券")
    @ExcelProperty(value = {"东方证券"})
    @JSONField(ordinal = 210)
    private String dfzq;

    /**
     * 浙商证券
     */
    @TableField(value = "zheshangzq")
    @Schema(description = "浙商证券")
    @ExcelProperty(value = {"浙商证券"})
    @JSONField(ordinal = 220)
    private String zheshangzq;

    /**
     * 光大证券
     */
    @TableField(value = "gdzq")
    @Schema(description = "光大证券")
    @ExcelProperty(value = {"光大证券"})
    @JSONField(ordinal = 230)
    private String gdzq;

    /**
     * 东方财富
     */
    @TableField(value = "dczq")
    @Schema(description = "东方财富")
    @ExcelProperty(value = {"东方财富"})
    @JSONField(ordinal = 240)
    private String dczq;

    /**
     * 天风证券
     */
    @TableField(value = "tfzq")
    @Schema(description = "天风证券")
    @ExcelProperty(value = {"天风证券"})
    @JSONField(ordinal = 250)
    private String tfzq;

    /**
     * 兴业证券
     */
    @TableField(value = "xyzq")
    @Schema(description = "兴业证券")
    @ExcelProperty(value = {"兴业证券"})
    @JSONField(ordinal = 260)
    private String xyzq;

    /**
     * 中泰证券
     */
    @TableField(value = "ztzq")
    @Schema(description = "中泰证券")
    @ExcelProperty(value = {"中泰证券"})
    @JSONField(ordinal = 270)
    private String ztzq;

    /**
     * 西部证券
     */
    @TableField(value = "xbzq")
    @Schema(description = "西部证券")
    @ExcelProperty(value = {"西部证券"})
    @JSONField(ordinal = 280)
    private String xbzq;

    /**
     * 长江证券
     */
    @TableField(value = "cjzq")
    @Schema(description = "长江证券")
    @ExcelProperty(value = "长江证券")
    @JSONField(ordinal = 290)
    private String cjzq;

    /**
     * 国金证券
     */
    @TableField(value = "gjzq")
    @Schema(description = "国金证券")
    @ExcelProperty(value = {"国金证券"})
    @JSONField(ordinal = 300)
    private String gjzq;

    /**
     * 东北证券
     */
    @TableField(value = "dbzq")
    @Schema(description = "东北证券")
    @ExcelProperty(value = {"东北证券"})
    @JSONField(ordinal = 310)
    private String dbzq;

    /**
     * 国元证券
     */
    @TableField(value = "gyzq")
    @Schema(description = "国元证券")
    @ExcelProperty(value = {"国元证券"})
    @JSONField(ordinal = 320)
    private String gyzq;

    /**
     * 财通证券
     */
    @TableField(value = "ctzq")
    @Schema(description = "财通证券")
    @ExcelProperty(value = {"财通证券"})
    @JSONField(ordinal = 330)
    private String ctzq;

    /**
     * 东兴证券
     */
    @TableField(value = "dxzq")
    @Schema(description = "东兴证券")
    @ExcelProperty(value = {"东兴证券"})
    @JSONField(ordinal = 340)
    private String dxzq;

    /**
     * 长城证券
     */
    @TableField(value = "cczq")
    @Schema(description = "长城证券")
    @ExcelProperty(value = {"长城证券"})
    @JSONField(ordinal = 350)
    private String cczq;

    /**
     * 华西证券
     */
    @TableField(value = "hxzq")
    @Schema(description = "华西证券")
    @ExcelProperty(value = {"华西证券"})
    @JSONField(ordinal = 360)
    private String hxzq;

    /**
     * 中银证券
     */
    @TableField(value = "zyzq")
    @Schema(description = "中银证券")
    @ExcelProperty(value = {"中银证券"})
    @JSONField(ordinal = 370)
    private String zyzq;

    /**
     * 南京证券
     */
    @TableField(value = "njzq")
    @Schema(description = "南京证券")
    @ExcelProperty(value = {"南京证券"})
    @JSONField(ordinal = 380)
    private String njzq;

    /**
     * 西南证券
     */
    @TableField(value = "xnzq")
    @Schema(description = "西南证券")
    @ExcelProperty(value = {"西南证券"})
    @JSONField(ordinal = 390)
    private String xnzq;

    /**
     * 平安证券
     */
    @TableField(value = "pazq")
    @Schema(description = "平安证券")
    @ExcelProperty(value = {"平安证券"})
    @JSONField(ordinal = 400)
    private String pazq;


    /**
     * 安信证券
     */
    @TableField(value = "axzq")
    @Schema(description = "安信证券")
    @ExcelProperty(value = {"安信证券"})
    @JSONField(ordinal = 410)
    private String axzq;

}
