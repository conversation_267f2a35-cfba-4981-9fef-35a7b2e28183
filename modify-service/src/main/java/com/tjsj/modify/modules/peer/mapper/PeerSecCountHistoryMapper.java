package com.tjsj.modify.modules.peer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modify.modules.peer.model.entity.PeerSecCountHistoryDO;
import com.tjsj.modules.base.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/2/10 21:06
 * @description
 */

@Mapper
public interface PeerSecCountHistoryMapper extends IBaseMapper<PeerSecCountHistoryDO> {
    /**
     * 选择新担保品统计记录
     *
     * @param peerEnName 同业英文名称
     * @param startDate  开始日期
     * @return {@link List }<{@link PeerSecCountHistoryDO }>
     * <AUTHOR>
     * @date 2025/02/10
     */
    List<PeerSecCountHistoryDO> selectNewCollateralCountRecord(@Param("peerEnName") String peerEnName,
                                                               @Param("startDate") LocalDate startDate);

    /**
     * 选择新融资标的统计记录
     *
     * @param peerEnName 同业en名称
     * @param startDate  开始日期
     * @return {@link List }<{@link PeerSecCountHistoryDO }>
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    List<PeerSecCountHistoryDO> selectNewFinancingTargetCountRecord(@Param("peerEnName") String peerEnName,
                                                                    @Param("startDate") LocalDate startDate);

    /**
     * 选择新融券标的统计记录
     *
     * @param peerEnName 同业en名称
     * @param startDate  开始日期
     * @return {@link List }<{@link PeerSecCountHistoryDO }>
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    List<PeerSecCountHistoryDO> selectNewShortSellTargetCountRecord(@Param("peerEnName") String peerEnName,
                                                                    @Param("startDate") LocalDate startDate);

    /**
     * 选择新集中度分组统计记录
     *
     * @param peerEnName 同业en名称
     * @param startDate  开始日期
     * @return {@link List }<{@link PeerSecCountHistoryDO }>
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    List<PeerSecCountHistoryDO> selectNewConcentraGroupCountRecord(@Param("peerEnName") String peerEnName,
                                                                   @Param("startDate") LocalDate startDate);
}