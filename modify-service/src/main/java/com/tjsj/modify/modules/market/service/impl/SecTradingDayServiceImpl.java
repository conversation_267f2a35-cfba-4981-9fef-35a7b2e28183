package com.tjsj.modify.modules.market.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.market.mapper.MarketUpdateUtilMapper;
import com.tjsj.modify.modules.market.mapper.SecTradingDayMapper;
import com.tjsj.modify.modules.market.model.entity.SecTradingDayDO;
import com.tjsj.modify.modules.market.service.SecTradingDayService;
import com.tjsj.modify.modules.market.service.StockMarketHisService;
import com.tjsj.modify.modules.stock.service.StockInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/11 14:20
 * @description
 */

@Service
@Slf4j
public class SecTradingDayServiceImpl extends ServiceImpl<SecTradingDayMapper, SecTradingDayDO>
        implements SecTradingDayService {
    @Resource
    private StockInfoService stockInfoService;

    @Resource
    private StockMarketHisService stockMarketHisService;

    @Resource
    private MarketUpdateUtilMapper marketUpdateUtilMapper;

    @Override
    public Boolean checkConsecutiveReachLimitCountSatisfy(List<Integer> tradingNumberList, int reachLimitCount) {
        // 记录当前连续数字的个数
        int consecutiveCount = 1;

        for (int i = 1; i < tradingNumberList.size(); i++) {
            // 判断是否连续
            if (tradingNumberList.get(i) == tradingNumberList.get(i - 1) + 1) {
                // 连续数字，计数加 1
                consecutiveCount++;
                if (consecutiveCount >= reachLimitCount) {
                    // 如果连续数字数量达到目标，返回 true
                    return true;
                }
            } else {
                // 不连续，重置计数
                consecutiveCount = 1;
            }
        }

        // 遍历结束后未找到满足条件的连续数字
        return false;
    }

    @Override
    public void autoUpdateStockTradingDayData() {
        long startTime = System.currentTimeMillis();

        // 用于存储需要删除的记录ID和需要新增的记录
        List<Integer> removeRecordIdList = new ArrayList<>();
        List<String> removeRecordSecCodeList = new ArrayList<>();
        List<SecTradingDayDO> newRecordList = new ArrayList<>();

        // 获取数据（最近一个月的新交易日、最小交易日和旧交易日）
        List<SecTradingDayDO> newStockTradingDayList = marketUpdateUtilMapper.selectNewStockTradingDay();
        List<SecTradingDayDO> secMinTradingDateList = marketUpdateUtilMapper.selectMinTradingDay();
        List<SecTradingDayDO> oldStockTradingDayList = marketUpdateUtilMapper.selectOldStockTradingDay();
        List<SecTradingDayDO> beforeMonthSecMaxTradingDateList =
                marketUpdateUtilMapper.selectBeforeMonthSecMaxTradingDate();

        // 根据股票代码进行分组
        Map<String, List<SecTradingDayDO>> newSecCodeTradingDayMap = newStockTradingDayList.stream()
                .collect(Collectors.groupingBy(SecTradingDayDO::getSecCode));
        Map<String, SecTradingDayDO> secCodeMinTradingDateMap = secMinTradingDateList.stream()
                .collect(Collectors.toMap(SecTradingDayDO::getSecCode, Function.identity()));
        Map<String, List<SecTradingDayDO>> secCodeOldTradingDateMap = oldStockTradingDayList.stream()
                .collect(Collectors.groupingBy(SecTradingDayDO::getSecCode));
        Map<String, SecTradingDayDO> secCodeBeforeMonthSecMaxTradingDateMap = beforeMonthSecMaxTradingDateList.stream()
                .collect(Collectors.toMap(SecTradingDayDO::getSecCode, Function.identity()));

        // 针对每个股票代码进行处理
        newSecCodeTradingDayMap.forEach((secCode, secCodetradingDayList) -> {
            // 按交易日期从小到大排序
            secCodetradingDayList.sort(Comparator.comparing(SecTradingDayDO::getDate));

            SecTradingDayDO minTradingDate = secCodeMinTradingDateMap.get(secCode);
            // 如果没有最小交易日期，则直接依次设置交易日序号，并重新生成该股票的所有历史交易日记录
            if (minTradingDate == null) {
                if (!secCodeBeforeMonthSecMaxTradingDateMap.containsKey(secCode)) {

                    for (int i = 0; i < secCodetradingDayList.size(); i++) {
                        secCodetradingDayList.get(i).setTradingNumber(i + 1);
                    }
                    removeRecordSecCodeList.add(secCode);

                } else {

                    Integer tradingNumber = secCodeBeforeMonthSecMaxTradingDateMap.get(secCode).getTradingNumber();
                    for (int i = 0; i < secCodetradingDayList.size(); i++) {
                        secCodetradingDayList.get(i).setTradingNumber(tradingNumber + i + 1);
                    }
                }
                newRecordList.addAll(secCodetradingDayList);

            } else {

                int baseTradingNumber = minTradingDate.getTradingNumber();
                // 过滤出交易日期不早于最小日期的记录
                List<SecTradingDayDO> filteredTradingDays = secCodetradingDayList.stream()
                        .filter(td -> !td.getDate().isBefore(minTradingDate.getDate()))
                        .toList();
                // 设置交易日序号：在原有的基础上累加
                for (int i = 0; i < filteredTradingDays.size(); i++) {
                    filteredTradingDays.get(i).setTradingNumber(baseTradingNumber + i);
                }

                // 获取对应的旧记录列表（防止为 null）
                List<SecTradingDayDO> oldRecordList = secCodeOldTradingDateMap.getOrDefault(secCode,
                        Collections.emptyList());

                // 构建以 [secCode, date, tradingNumber] 为 key 的映射，避免重复计算唯一 key
                Map<String, SecTradingDayDO> oldKeyMap = oldRecordList.stream()
                        .collect(Collectors.toMap(
                                record -> ModifyDataUtil.buildUniqueKey(
                                        record.getSecCode(),
                                        record.getDate(),
                                        record.getTradingNumber()),
                                Function.identity()
                        ));
                Map<String, SecTradingDayDO> newKeyMap = filteredTradingDays.stream()
                        .collect(Collectors.toMap(
                                record -> ModifyDataUtil.buildUniqueKey(
                                        record.getSecCode(),
                                        record.getDate(),
                                        record.getTradingNumber()),
                                Function.identity()
                        ));

                // 利用 keySet 差集计算需要删除的记录和需要新增的记录
                Set<String> oldKeys = oldKeyMap.keySet();
                Set<String> newKeys = newKeyMap.keySet();

                // 删除：旧记录中存在但新记录中不存在
                List<Integer> idsToRemove = oldKeys.stream()
                        .filter(key -> !newKeys.contains(key))
                        .map(key -> oldKeyMap.get(key).getId())
                        .toList();
                removeRecordIdList.addAll(idsToRemove);

                // 新增：新记录中存在但旧记录中不存在
                List<SecTradingDayDO> recordsToInsert = newKeys.stream()
                        .filter(key -> !oldKeys.contains(key))
                        .map(newKeyMap::get)
                        .toList();
                newRecordList.addAll(recordsToInsert);
            }

        });

        //  log.warn("新增记录数量：{}，删除记录数量：{}", newRecordList.size(), removeRecordIdList.size());

        // 执行批量删除和插入操作
        if (!removeRecordIdList.isEmpty()) {
            this.removeByIds(removeRecordIdList);
        }
        if (!removeRecordSecCodeList.isEmpty()) {
            this.remove(Wrappers.<SecTradingDayDO>lambdaQuery()
                    .in(SecTradingDayDO::getSecCode, removeRecordSecCodeList));
        }

        if (!newRecordList.isEmpty()) {
            this.baseMapper.insertBatchSomeColumn(newRecordList);
        }

        long endTime = System.currentTimeMillis();
//        log.warn("更新交易日数据完成，用时：{} ms", endTime - startTime);

    }


}
