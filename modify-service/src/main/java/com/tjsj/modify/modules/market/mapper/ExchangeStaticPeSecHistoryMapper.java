package com.tjsj.modify.modules.market.mapper;

import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecHistoryDO;
import com.tjsj.modules.base.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/29 11:44
 * @description
 */
@Mapper
public interface ExchangeStaticPeSecHistoryMapper extends IBaseMapper<ExchangeStaticPeSecHistoryDO> {


	/**
	 * 获取历史日期
	 *
	 * @return {@link List }<{@link String }>
	 * <AUTHOR> Ye
	 * @date 2025/05/29
	 */
	List<String> getHistoryDate();

	/**
	 * 插入历史数据
	 *
	 * @param date 日期
	 * <AUTHOR>
	 * @date 2025/05/29
	 */
	void insertExchangeStaticPeDateData(@Param("date") String date);


}