package com.tjsj.modify.modules.market.controller;

import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.env.ProfileEnvSetting;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.common.enums.basic.ProfileTypeEnum;
import com.tjsj.common.utils.data.UpdateRealUtil;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecAdjustHisService;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecHistoryService;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecService;
import com.tjsj.modify.modules.market.service.SecTradingDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * MarketController
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/2/5 19:38
 * @description 市场行情
 */
@Tag(name = "MarketController", description = "市场行情")
@RestController
@RequestMapping("/market")
@RequiredArgsConstructor
@Slf4j
@Validated
public class MarketController {

    private final SecTradingDayService secTradingDayService;

    private final ExchangeStaticPeSecService exchangeStaticPeSecService;

    private final ExchangeStaticPeSecHistoryService exchangeStaticPeSecHistoryService;

    private final ExchangeStaticPeSecAdjustHisService exchangeStaticPeSecAdjustHisService;

    private final UpdateRealUtil updateRealUtil;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Operation(summary = "自动更新交易所静态市盈率外规数据")
    @GetMapping("/autoUpdateExchangeStaticPeSecData")
    @PassToken
    @Scheduled(fixedDelay = 10000)
    @SingleInstanceLock
    public void autoUpdateExchangeStaticPeSecData() {
        if (!activeProfile.equals(ProfileTypeEnum.PROD.getCode())) {
            return;
        }

        // 交易所静止市盈率外规证券数据 tj_middle_ground.t_exchange_static_pe_sec
        updateRealUtil.executeUpdateMethod(exchangeStaticPeSecService::autoUpdateExchangeStaticPeSecData,
                "autoUpdateExchangeStaticPeSecData");
//		updateRealUtil.executeUpdateMethod(exchangeStaticPeSecHistoryService::autoUpdateExchangeStaticPeSecDataHistory,
//				"autoUpdateExchangeStaticPeSecDataHistory");
        updateRealUtil.executeUpdateMethod(
                exchangeStaticPeSecAdjustHisService::autoUpdateExchangeStaticPeSecAdjustHisData,
                "更新交易所静态市盈率证券调整历史数据");
        updateRealUtil.executeUpdateMethod(
                exchangeStaticPeSecHistoryService::autoUpdateExchangeStaticPeSecDataHistoryNew,
                "更新交易所静态市盈率证券历史数据");

    }

    @Operation(summary = "自动更新股票交易日期数据")
    @GetMapping("/autoUpdateStockTradingDayData")
    @PassToken
    @Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟更新一次
    @ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
    @SingleInstanceLock
    public void autoUpdateStockTradingDayData() {

        if (!activeProfile.equals(ProfileTypeEnum.PROD.getCode())) {
            return;
        }

        updateRealUtil.executeUpdateMethod(secTradingDayService::autoUpdateStockTradingDayData,
                "autoUpdateStockTradingDayData");

    }


}
