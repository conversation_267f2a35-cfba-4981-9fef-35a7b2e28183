package com.tjsj.modify.modules.peer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.enums.basic.CommonStatusEnum;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import com.tjsj.modify.modules.peer.mapper.PeerDataMapper;
import com.tjsj.modify.modules.peer.mapper.PeerSecuritiesSettingMapper;
import com.tjsj.modify.modules.peer.model.entity.PeerSecuritiesSetting;
import com.tjsj.modify.modules.peer.model.vo.PeerNameVO;
import com.tjsj.modify.modules.peer.service.PeerSecuritiesSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PeerSecuritiesSettingServiceImpl
 *
 * <AUTHOR>
 * @date 2024/08/13
 * @description 针对表【t_finance_settings(同业券商信息表)】的数据库操作Service实现
 */
@Service
@Slf4j
public class PeerSecuritiesSettingServiceImpl extends ServiceImpl<PeerSecuritiesSettingMapper, PeerSecuritiesSetting>
        implements PeerSecuritiesSettingService {

    @Resource
    private PeerDataMapper peerDataMapper;

    @Override
    public List<PeerNameVO> getPeerAgencyList() {
        return this.getPeerAgencyList(false, null);
    }

    @Override
    public List<PeerSecuritiesSetting> getPeerAgencyInfoList() {
        return this.getPeerAgencyInfoList(null);
    }

    @Override
    public List<PeerSecuritiesSetting> getPeerAgencyInfoList(Boolean ifNormal) {
        return this.list(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                .eq(null != ifNormal && ifNormal, PeerSecuritiesSetting::getMark2,
                        PeerConsts.PEER_SECURITIES_STATUS_NORMAL));
    }

    @Override
    public List<PeerSecuritiesSetting> queryPeerAgencyList(Boolean ifNormal) {

        return this.queryPeerAgencyList(ifNormal, null);
    }

    @Override
    public List<PeerNameVO> getPeerAgencyList(boolean ifLevelMark, Boolean ifColumnShow) {
        return queryPeerAgencyList(true, ifColumnShow)
                .stream()
                .map(peerSecuritiesSetting -> {
                    if (ifLevelMark && !peerSecuritiesSetting.getJzdCls().equals(0)) {
                        peerSecuritiesSetting.setCnName(peerSecuritiesSetting.getCnName() + " √");
                    }
                    return BeanUtil.copyProperties(peerSecuritiesSetting, PeerNameVO.class);
                })
                .collect(Collectors.toList());
    }


    @Override
    public List<PeerSecuritiesSetting> queryPeerAgencyList(Boolean ifNormal, Boolean ifColumnShow) {

        return this.list(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                .eq(PeerSecuritiesSetting::getEnableStatus, CommonStatusEnum.ENABLE)
                .eq(null == ifNormal || ifNormal, PeerSecuritiesSetting::getMark2,
                        PeerConsts.PEER_SECURITIES_STATUS_NORMAL)
                .eq(null != ifColumnShow, PeerSecuritiesSetting::getIfColumnShow, CommonStatusEnum.ENABLE)
                .groupBy(PeerSecuritiesSetting::getEnName)
                .orderByAsc(PeerSecuritiesSetting::getRanking));
    }

    @Override
    public String queryEnNameByCnName(String cnName) {
        return this.getOne(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                .eq(PeerSecuritiesSetting::getCnName, cnName)).getEnName();
    }

    @Override
    public String queryCnNameByEnName(String enName) {
        return this.getOne(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                        .eq(PeerSecuritiesSetting::getEnName, enName)
                        .last("LIMIT 1"))
                .getCnName();
    }

    @Override
    public List<PeerSecuritiesSetting> getPeerSecuritiesBaseInfos(String enName, Integer ifEnable) {
        return this.list(Wrappers.<PeerSecuritiesSetting>lambdaQuery()
                .select(PeerSecuritiesSetting::getEnName, PeerSecuritiesSetting::getCnName,
                        PeerSecuritiesSetting::getJzdUrl)
                .eq(StrUtil.isNotBlank(enName), PeerSecuritiesSetting::getEnName, enName)
                .eq(ifEnable != null, PeerSecuritiesSetting::getMark2, ifEnable)
                .orderByAsc(PeerSecuritiesSetting::getRanking)
                .orderByDesc(PeerSecuritiesSetting::getId));
    }

    @Override
    public List<String> getValidPeerSecuritiesTableNameList() {

        return peerDataMapper.selectValidPeerSecuritiesTableNameList();
    }

}




