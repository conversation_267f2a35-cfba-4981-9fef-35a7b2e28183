package com.tjsj.modify.modules.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.market.model.entity.SecTradingDayDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/11 14:20
 * @description 证券交易日信息服务接口
 */
public interface SecTradingDayService extends IService<SecTradingDayDO> {


    /**
     * 判断连续涨跌停个数条件满足
     *
     * @param tradingNumberList 交易日序号列表
     * @param reachLimitCount   连续涨跌停个数
     * @return {@link Boolean }
     * <AUTHOR> Ye
     * @date 2024/11/27
     */
    Boolean checkConsecutiveReachLimitCountSatisfy(List<Integer> tradingNumberList, int reachLimitCount);

    /**
     * 自动更新股票交易日期数据
     *
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    void autoUpdateStockTradingDayData();

}
