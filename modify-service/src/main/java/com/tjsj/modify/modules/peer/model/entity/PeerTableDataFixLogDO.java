package com.tjsj.modify.modules.peer.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * PeerTableDataFixLogDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description 同业数据表修复日志流水
 * @date 2025/4/16 20:12
 */
@Schema(description = "同业数据表修复日志流水")
@Data
@Accessors(chain = true)
@TableName(value = "credit.t_peer_table_data_fix_log")
@Alias("PeerTableDataFixLogDO")
public class PeerTableDataFixLogDO implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	@Schema(description = "")
	private Integer id;

	/**
	 * 表名
	 */
	@Schema(description = "表名")
	private String tableName;

	/**
	 * 开始时间
	 */
	@Schema(description = "开始时间")
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	@Schema(description = "结束时间")
	private LocalDateTime endTime;

	/**
	 * 日期
	 */
	@Schema(description = "日期")
	private LocalDate date;

	/**
	 * 执行耗时（毫秒）
	 */
	@Schema(description = "执行耗时（毫秒）")
	private Long timeDuration;

	@Schema(description = "")
	@TableField(fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	@Schema(description = "")
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	@Serial
	private static final long serialVersionUID = 1L;
}