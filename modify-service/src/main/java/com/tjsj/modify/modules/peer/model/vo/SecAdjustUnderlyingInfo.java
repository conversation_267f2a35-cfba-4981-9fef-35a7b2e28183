package com.tjsj.modify.modules.peer.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.annotation.JSONField;
import com.tjsj.common.annotation.calculate.CalculateField;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

/**
 * SecAdjustUnderlyingInfo
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/20 10:25
 * @description 同业券商-标的证券调整信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@FieldNameConstants
@Schema(description = "同业券商-标的证券调整信息")
public class SecAdjustUnderlyingInfo extends SecAdjustBaseModel {

    /**
     * 调出融资标的数量
     */
    @JSONField(ordinal = 10)
    @CalculateField(value = PeerConsts.MARGIN_SECURITY_OUT)
    @Schema(description = "调出融资标的数量")
    @ExcelProperty(value = "调出融资标的数量", order = 100)
    private Integer shortSellingTargetOutCount = 0;

    /**
     * 调出融券标的数量
     */
    @JSONField(ordinal = 20)
    @CalculateField(value = PeerConsts.SHORT_SECURITY_OUT)
    @Schema(description = "调出融券标的数量")
    @ExcelProperty(value = "调出融券标的数量", order = 200)
    private Integer marginTradingTargetOutCount = 0;

    /**
     * 融资保证金比例调低数量
     */
    @JSONField(ordinal = 30)
    @CalculateField(value = PeerConsts.FINANCE_MARGIN_RATIO_REDUCE)
    @Schema(description = "融资保证金比例调低数量")
    @ExcelProperty(value = "融资保证金比例调低数量", order = 300)
    private Integer financeMarginRatioReduceCount = 0;

    /**
     * 融券保证金比例调低数量
     */
    @JSONField(ordinal = 40)
    @CalculateField(value = PeerConsts.SHORT_MARGIN_RATIO_REDUCE)
    @Schema(description = "融券保证金比例调低数量")
    @ExcelProperty(value = "融券保证金比例调低数量", order = 400)
    private Integer shortMarginRatioReduceCount = 0;

    /**
     * 融资保证金比例调高数量
     */
    @JSONField(ordinal = 50)
    @CalculateField(value = PeerConsts.FINANCE_MARGIN_RATIO_INCREASE)
    @Schema(description = "融资保证金比例调高数量")
    @ExcelProperty(value = "融资保证金比例调高数量", order = 500)
    private Integer financeMarginRatioIncreaseCount = 0;

    /**
     * 融券保证金比例调高数量
     */
    @JSONField(ordinal = 60)
    @CalculateField(value = PeerConsts.SHORT_MARGIN_RATIO_INCREASE)
    @Schema(description = "融券保证金比例调高数量")
    @ExcelProperty(value = "融券保证金比例调高数量", order = 600)
    private Integer shortMarginRatioIncreaseCount = 0;


}
