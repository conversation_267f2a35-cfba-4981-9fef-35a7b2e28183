package com.tjsj.modify.modules.margin.business.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modify.modules.common.enums.MrgTrdDataType;
import com.tjsj.modify.modules.margin.business.mapper.ExchangeRuleThresholdConfigMapper;
import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdConfigDO;
import com.tjsj.modify.modules.margin.business.service.ExchangeRuleThresholdConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * ExchangeRuleThresholdConfigServiceImpl
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/02/19
 * @description 交易所规则阈值配置服务实现类
 */
@Service
public class ExchangeRuleThresholdConfigServiceImpl
        extends ServiceImpl<ExchangeRuleThresholdConfigMapper, ExchangeRuleThresholdConfigDO>
        implements ExchangeRuleThresholdConfigService {

    @Override
    public int insertOrUpdate(ExchangeRuleThresholdConfigDO record) {
        return baseMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ExchangeRuleThresholdConfigDO record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    @Override
    public List<ExchangeRuleThresholdConfigDO> getMarginRuleThresholdConfig(MrgTrdDataType dataType) {
        return this.list(Wrappers.<ExchangeRuleThresholdConfigDO>lambdaQuery()
                .eq(null != dataType, ExchangeRuleThresholdConfigDO::getMrdTrdType, dataType)
                .orderByAsc(ExchangeRuleThresholdConfigDO::getId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyMarginRuleThresholdConfig(MrgTrdDataType dataType,
                                                List<ExchangeRuleThresholdConfigDO> exchangeRuleThresholdConfigs) {

        this.remove(Wrappers.<ExchangeRuleThresholdConfigDO>lambdaQuery()
                .eq(null != dataType, ExchangeRuleThresholdConfigDO::getMrdTrdType, dataType));

        exchangeRuleThresholdConfigs.forEach(config -> config.setMrdTrdType(dataType));
        this.saveBatch(exchangeRuleThresholdConfigs);

    }
}
