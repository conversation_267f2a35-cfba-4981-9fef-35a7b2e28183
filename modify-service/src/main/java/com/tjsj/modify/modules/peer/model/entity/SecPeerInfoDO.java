package com.tjsj.modify.modules.peer.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.basic.CommonStatusEnum;
import com.tjsj.common.enums.basic.SecTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SecPeerInfoDO
 *
 * <AUTHOR>
 * @date 2024/8/9 10:35
 * @version 1.0.0
 * @description 同业券商-最新两融业务数据
 */
@Schema(description = "同业券商-最新两融业务数据")
@Data
@Accessors(chain = true)
@TableName(value = "tj_middle_ground.t_sec_peer_info")
@Alias(value = "SecPeerInfoDO")
public class SecPeerInfoDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    @JSONField(serialize = false)
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    /**
     * 证券代码带后缀
     */
    @TableField(value = "`code`")
    @Schema(description = "证券代码带后缀")
    private String code;

    /**
     * 证券名称
     */
    @TableField(value = "sec_name")
    @Schema(description = "证券名称")
    private String secName;


    /**
     * 证券类型
     */
    @TableField(value = "sec_type")
    @Schema(description = "证券类型")
    private SecTypeEnum secType;

    /**
     * 最新日期
     */
    @TableField(value = "`date`")
    @Schema(description = "最新日期")
    private String date;

    /**
     * 担保品折算率
     */
    @TableField(value = "collateral_haircut")
    @Schema(description = "担保品折算率")
    private BigDecimal collateralHaircut;

    /**
     * 是否担保品
     */
    @TableField(value = "is_collateral")
    @Schema(description = "是否担保品")
    private CommonStatusEnum collateral;

    /**
     * 证券分组
     */
    @TableField(value = "sec_category")
    @Schema(description = "证券分组")
    private String secCategory;

    /**
     * 融资保证金比例
     */
    @TableField(value = "finance_margin_ratio")
    @Schema(description = "融资保证金比例")
    private BigDecimal financeMarginRatio;

    /**
     * 融券保证金比例
     */
    @TableField(value = "short_margin_ratio")
    @Schema(description = "融券保证金比例")
    private BigDecimal shortMarginRatio;

    /**
     * 券商中文名称
     */
    @TableField(value = "`source`")
    @Schema(description = "券商中文名称")
    private String source;

    /**
     * 是否融资标的
     */
    @TableField(value = "is_financing_target")
    @Schema(description = "是否融资标的")
    private CommonStatusEnum financingTarget;

    /**
     * 是否融券标的
     */
    @TableField(value = "is_short_sell_target")
    @Schema(description = "是否融券标的")
    private CommonStatusEnum shortSellTarget;

    @TableField(value = "concentration")
    @Schema(description = "集中度范围")
    private String concentration;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    @JSONField(serialize = false)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    @JSONField(serialize = false)
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}