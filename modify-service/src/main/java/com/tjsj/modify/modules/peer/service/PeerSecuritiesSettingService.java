package com.tjsj.modify.modules.peer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.peer.model.entity.PeerSecuritiesSetting;
import com.tjsj.modify.modules.peer.model.vo.PeerNameVO;

import java.util.List;

/**
 * PeerSecuritiesSettingService
 *
 * <AUTHOR> Ye
 * @date 2024/08/09
 * @description 针对表【t_finance_settings(同业券商信息表)】的数据库操作Service
 */
public interface PeerSecuritiesSettingService extends IService<PeerSecuritiesSetting> {

    /**
     * 获取同业券商列表
     *
     * @param ifLevelMark  是否存在评级标记
     * @param ifColumnShow 是否显示列
     * @return List<PeerNameVo> 同业券商列表
     * <AUTHOR> Ye
     * @date 2024/07/19
     */
    List<PeerNameVO> getPeerAgencyList(boolean ifLevelMark, Boolean ifColumnShow);

    /**
     * 获取同业机构列表
     *
     * @return {@link List }<{@link PeerNameVO }>
     * <AUTHOR> Ye
     * @date 2025/01/02
     */
    List<PeerNameVO> getPeerAgencyList();

    List<PeerSecuritiesSetting> getPeerAgencyInfoList();

    List<PeerSecuritiesSetting> getPeerAgencyInfoList(Boolean ifNormal);

    /**
     * 从数据库中获取同业券商列表
     *
     * @param ifNormal 是否状态正常
     * @return List<PeerSecuritiesSetting> 同业券商列表
     * <AUTHOR> Ye
     * @date 2024/08/13
     */
    List<PeerSecuritiesSetting> queryPeerAgencyList(Boolean ifNormal);

    /**
     * 从数据库中获取同业券商列表
     *
     * @param ifNormal     是否状态正常
     * @param ifColumnShow 是否显示列
     * @return List<PeerSecuritiesSetting> 同业券商列表
     * <AUTHOR> Ye
     * @date 2024/08/13
     */
    List<PeerSecuritiesSetting> queryPeerAgencyList(Boolean ifNormal, Boolean ifColumnShow);


    /**
     * 按cn名称查询en名称
     *
     * @param cnName cn名称
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/07/20
     */
    String queryEnNameByCnName(String cnName);

    /**
     * 按中文名查询中文名
     *
     * @param enName 英文名称
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/07/20
     */
    String queryCnNameByEnName(String enName);

    /**
     * 获取同业证券基础信息
     *
     * @param enName   en姓名
     * @param ifEnable 如果启用
     * @return {@link PeerSecuritiesSetting }
     * <AUTHOR> Ye
     * @date 2025/01/06
     */
    List<PeerSecuritiesSetting> getPeerSecuritiesBaseInfos(String enName, Integer ifEnable);

    /**
     * 获取有效的同业证券的所有表名称列表
     *
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    List<String> getValidPeerSecuritiesTableNameList();

}
