package com.tjsj.modify.modules.peer.common.consts;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3 22:47
 * @description 同业券商常量
 */
@UtilityClass
@Schema(description = "同业券商常量")
public class PeerConsts {

	public final String TF_SECURITIES_CN = "天风证券";

	/**
	 * 同业券商数据表名统一前缀
	 */
	public final String PEER_PREFIX = "t_rzrq_";

	/**
	 * 同业券商数据表名统一前缀
	 */
	public final String HIS_PEER_PREFIX = "t_margin_";

	/**
	 * 折算率表后缀
	 */
	public final String COLLATERAL_SUFFIX = "_zsl";


	public final String COLLATERAL_HIS_SUFFIX = "_collateral";

	/**
	 * 集中度表后缀
	 */
	public final String CONCENTRATION_SUFFIX = "_jzd";

	public final String CATEGORY_HIS_SUFFIX = "_category";

	/**
	 * 标的证券后缀
	 */
	public final String UNDERLYING_SUFFIX = "_bdzq";

	public final String UNDERLYING_HIS_SUFFIX = "_underlying";


	/**
	 * 后缀表名列表
	 */
	public final List<String> SUFFIX_LIST = Arrays.asList(COLLATERAL_SUFFIX,
			CONCENTRATION_SUFFIX, UNDERLYING_SUFFIX);


	/**
	 * 同业折算率调低
	 */
	public final String COLLATERAL_HAIRCUT_REDUCE = "同业折算率调低";

	/**
	 * 同业折算率调高
	 */
	public final String COLLATERAL_HAIRCUT_INCREASE = "同业折算率调高";

	public final String PEER_PEER_LEVEL_ADJUST = "同业评级调整";

	/**
	 * 同业评级调出
	 */
	public final String CATEGORY_OUT = "同业评级调出";

	/**
	 * 同业评级调低
	 */
	public final String CATEGORY_DOWN = "同业评级调低";

	/**
	 * 同业评级调高
	 */
	public final String CATEGORY_UP = "同业评级调高";

	/**
	 * 同业担保品调出
	 */
	public final String COLLATERAL_OUT = "同业担保品调出";

	/**
	 * 同业担保品调入
	 */
	public final String COLLATERAL_IN = "同业担保品调入";

	/**
	 * 同业调出融资标的
	 */
	public final String MARGIN_SECURITY_OUT = "同业调出融资标的";

	/**
	 * 同业调出融券标的
	 */
	public final String SHORT_SECURITY_OUT = "同业调出融券标的";

	/**
	 * 同业融资保证金比例调低
	 */
	public final String FINANCE_MARGIN_RATIO_REDUCE = "同业融资保证金比例调低";

	/**
	 * 同业融券保证金比例调低
	 */
	public final String SHORT_MARGIN_RATIO_REDUCE = "同业融券保证金比例调低";

	/**
	 * 同业融资保证金比例调高
	 */
	public final String FINANCE_MARGIN_RATIO_INCREASE = "同业融资保证金比例调高";

	/**
	 * 同业融券保证金比例调高
	 */
	public final String SHORT_MARGIN_RATIO_INCREASE = "同业融券保证金比例调高";

	/**
	 * 同业证券状态-正常
	 */
	public final Integer PEER_SECURITIES_STATUS_NORMAL = 1;

	/**
	 * 表字段-enable_status
	 */
	public final String TABLE_FIELD_ENABLE_STATUS = "enable_status";

	/**
	 * 表字段-if_market_checked
	 */
	public final String TABLE_FIELD_IF_MARKET_CHECKED = "if_market_checked";


}
