package com.tjsj.modify.modules.margin.business.controller;

import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.common.enums.basic.ProfileTypeEnum;
import com.tjsj.modify.modules.margin.business.service.ExchangeRuleThresholdSecService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * MrgTrdBusinessController
 *
 * <AUTHOR>
 * @date 2025/02/21
 * @description 融资融券/业务相关
 * @version 1.0.0
 */
@Tag(name = "MrgTrdBusinessController", description = "融资融券/业务相关")
@RestController
@RequestMapping("/mrgTrdBusiness")
@RequiredArgsConstructor
@Slf4j
@Validated
public class MrgTrdBusinessController {

    private final ExchangeRuleThresholdSecService exchangeRuleThresholdSecService;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Operation(summary = "自动更新交易所外规阈值对应的证券具体值")
    @GetMapping("/autoUpdateExchangeRuleThresholdSecData")
    @PassToken
    @Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟执行一次
    @SingleInstanceLock
    public void autoUpdateExchangeRuleThresholdSecData() {
        if (!activeProfile.equals(ProfileTypeEnum.PROD.getCode())) {
            return;
        }

        exchangeRuleThresholdSecService.autoUpdateExchangeRuleThresholdSecData();

    }

}
