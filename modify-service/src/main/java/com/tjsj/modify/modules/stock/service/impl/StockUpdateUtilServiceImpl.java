package com.tjsj.modify.modules.stock.service.impl;

import com.tjsj.common.constants.cache.RedisConsts;
import com.tjsj.common.lock.DistributedLockHelper;
import com.tjsj.modify.modules.stock.mapper.StockUpdateUtilMapper;
import com.tjsj.modify.modules.stock.service.HolderShareFrozenLabelService;
import com.tjsj.modify.modules.stock.service.HolderShareFrozenService;
import com.tjsj.modify.modules.stock.service.StockUpdateUtilService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * StockUpdateUtilServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/2/8 21:50
 * @description StockUpdateUtilServiceImpl
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StockUpdateUtilServiceImpl implements StockUpdateUtilService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private DistributedLockHelper distributedLockHelper;

    @Resource
    private HolderShareFrozenLabelService holderShareFrozenLabelService;

    private final StockUpdateUtilMapper stockUpdateUtilMapper;

    private final HolderShareFrozenService holderShareFrozenService;


    @Override
    public void distinctHolderReductionTable() {
        stockUpdateUtilMapper.distinctHolderReductionTable();
    }

    @Override
    public void distinctCriminalRecordTable() {
        stockUpdateUtilMapper.distinctCriminalRecordTable();
    }

    @Override
    public void autoUpdateHolderShareFrozenTable() {
//		// 先修复 pledgedata.t_frozen 表
//		this.fixFrozenTable();
        // 更新 pledgedata.t_holder_share_frozen 表
        holderShareFrozenService.updateHolderShareFrozenTable();
        // 更新 labels.t_holder_share_frozen_label 表
        holderShareFrozenLabelService.updateHolderShareFrozenLabelTable();
    }

    @Override
    public void updateStockMarketPledgeRatio() {


        distributedLockHelper.executeWithLock(RedisConsts.T_STOCK_INFO_TABLE_LOCK,
                null,
                10L,
                stockUpdateUtilMapper::updateStockMarketPledgeRatio);

    }


    @Override
    public void updateStockMarketCollateralRatio() {


        distributedLockHelper.executeWithLock(RedisConsts.T_STOCK_INFO_TABLE_LOCK,
                null,
                10L,
                stockUpdateUtilMapper::updateStockMarketCollateralRatio);
    }

    /**
     * 修复 pledgedata.t_frozen 表
     *
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    @Deprecated
    private void fixFrozenTable() {
        // 更新 frozenPerson 字段
        //stockUpdateUtilMapper.updateHolderShareFrozenTableFrozenPersonField();
    }

}
