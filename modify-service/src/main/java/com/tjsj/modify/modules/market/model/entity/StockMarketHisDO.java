package com.tjsj.modify.modules.market.model.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * StockMarketHisDO
 *
 * <AUTHOR>
 * @date 2024/07/19
 * @description 股票市场历史
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
@TableName(value = "pledgedata.t_stockhistory_0827")
@Alias(value = "StockMarketHisDO")
@Schema(name = "StockMarketHisDO", description = "股票市场历史")
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class StockMarketHisDO implements Serializable {

    @Serial
    private static final long serialVersionUID = -730373352796996232L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 股票id
     */
    @Schema(description = "股票代码")
    @TableField("StockId")
    @ExcelProperty(value = "证券代码")
    private String stockId;

    @Schema(description = "日期")
    @TableField("Date")
    @JSONField(format = "yyyy-MM-dd")
    @ExcelProperty(value = "日期")
    private LocalDate date;

    @Schema(description = "开盘价")
    @TableField("Open")
    private BigDecimal open;

    @Schema(description = "最高")
    @TableField("High")
    private BigDecimal high;

    @Schema(description = "最低")
    @TableField("Low")
    private BigDecimal low;

    public static final String CLOSE_PRICE_FIELD = "`Close`";

    /**
     * 收盘价
     */
    @TableField(value = CLOSE_PRICE_FIELD)
    @Schema(description = "收盘价")
    private BigDecimal closePrice;

    @Schema(description = "昨收")
    @TableField("PreClose")
    private BigDecimal preClose;

    public static final String VOLUME_FIELD = "`Volume`";

    /**
     * 成交量（手）
     */
    @Schema(description = "成交量（手）", required = true)
    @TableField(value = VOLUME_FIELD)
    private BigDecimal volume;

    public static final String AMOUNT_FIELD = "`Amount`";

    /**
     * 成交额（元）
     */
    @Schema(description = "成交额（元）", required = true)
    @TableField(value = AMOUNT_FIELD)
    private BigDecimal amount;

    @Schema(description = "涨跌额")
    @TableField("ChangeAmount")
    private BigDecimal changeAmount;

    @Schema(description = "涨跌幅（%）")
    @TableField("ChangePrice")
    private BigDecimal changePrice;

    @Schema(description = "振幅（%）")
    @TableField("Swing")
    private BigDecimal swing;

    @Schema(description = "换手率（%）")
    @TableField("Turnover")
    private BigDecimal turnover;

    public static final String ROLLING_PE_FIELD = "TTM";

    /**
     * 滚动市盈率
     */
    @TableField(value = ROLLING_PE_FIELD)
    @Schema(description = "滚动市盈率", required = true)
    private BigDecimal ttm;

    public static final String PB_FIELD = "PB";


    public static final String STATIC_PE_FIELD = "PES";

    /**
     * 静态市盈率
     */
    @TableField(value = STATIC_PE_FIELD)
    @Schema(description = "静态市盈率", required = true)
    @ExcelProperty(value = "静态市盈率")
    private BigDecimal pes;

    @Schema(description = "市净率")
    @TableField(PB_FIELD)
    private BigDecimal pb;

    @Schema(description = "总股本")
    @TableField("TotalVolume")
    private BigDecimal totalVolume;

    /**
     * 总市值字段
     */
    public static final String TOTAL_AMOUNT_FIELD = "TotalAmount";

    @Schema(description = "总市值")
    @TableField(TOTAL_AMOUNT_FIELD)
    private BigDecimal totalAmount;

    public static final String CIRCULATION_AMOUNT_FIELD = "circulation_amount";

    @Schema(description = "流通市值")
    @TableField(value = CIRCULATION_AMOUNT_FIELD)
    private BigDecimal circulationAmount;

    @Schema(description = "流通股本")
    @TableField(value = "circulation_cost")
    private BigDecimal circulationCost;

    public static final String DYNAMIC_PE_FIELD = "TTM1";

    @TableField(value = DYNAMIC_PE_FIELD)
    @Schema(description = "动态市盈率", required = true)
    private BigDecimal ttm1;

    @Schema(description = "更新日期")
    @TableField("Updatedate")
    private LocalDateTime updateTime;

}
