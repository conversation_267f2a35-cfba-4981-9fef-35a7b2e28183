package com.tjsj.modify.modules.peer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.peer.model.entity.PeerSecCountHistoryDO;

/**
* <AUTHOR>
* @date 2025/2/10 21:06
* @description
* @version 1.0.0
*/

public interface PeerSecCountHistoryService extends IService<PeerSecCountHistoryDO>{


    /**
     * 更新同业证券统计历史
     *
     * @param startBeforeDays 几天前开始
     * <AUTHOR>
     * @date 2025/02/10
     */
    void updatePeerSecCountHistory(Integer startBeforeDays);
}
