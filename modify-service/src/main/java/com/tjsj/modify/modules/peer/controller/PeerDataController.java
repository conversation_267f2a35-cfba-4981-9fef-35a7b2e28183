package com.tjsj.modify.modules.peer.controller;


import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.annotation.ReviewDate;
import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.common.enums.basic.ProfileTypeEnum;
import com.tjsj.common.utils.data.UpdateRealUtil;
import com.tjsj.modify.modules.peer.service.PeerSecCountHistoryService;
import com.tjsj.modify.modules.peer.service.SecPeerInfoService;
import com.tjsj.modify.modules.peer.service.impl.PeerDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * PeerDataController
 *
 * <AUTHOR> Ye
 * @date 2024/07/16
 * @description 同业券商数据
 */
@Tag(name = "PeerDataController", description = "同业券商数据")
@RestController
@RequestMapping("peer-date")
@RequiredArgsConstructor
@Slf4j
@Validated
@ConditionalOnProperty(name = "tarkin.env", havingValue = "tarkin")
public class PeerDataController {
	@Resource
	private SecPeerInfoService secPeerInfoService;

	private final PeerDataService peerDataService;

	private final UpdateRealUtil updateRealUtil;

	private final PeerSecCountHistoryService peerSecCountHistoryService;

	@Value("${spring.profiles.active}")
	private String activeProfile;


	@Operation(summary = "自动同步同业券商数据")
	@GetMapping("/autoSyncPeerMarTradData")
	@PassToken
	@CheckCount(count = 1)
	@ReviewDate(reviewDates = {"2024-11-25 18:59"})
	// 每10秒执行一次
	@Scheduled(fixedDelay = 1000 * 10)
	@SingleInstanceLock
	public void autoSyncPeerMarTradData() {

		if (!activeProfile.equals(ProfileTypeEnum.PROD.getCode())) {
			return;
		}

		// 更新同业券商数据详情 tj_middle_ground.t_sec_peer_info表
		secPeerInfoService.updateSecPeerInfoTable();

		// 修复同业券商数据表
		peerDataService.fixPeerTableData();
		log.info("fixPeerTableData方法执行完毕，当前时间：{}", LocalDateTime.now());

		// 更新margin.t_peer_sec_xxx_data表中的数据
		updateRealUtil.executeUpdateMethod(peerDataService::updatePeerSecData, "updatePeerSecData");
		log.info("updatePeerSecData方法执行完毕，当前时间：{}", LocalDateTime.now());

		// 更新margin.t_peer_sec_count_history 同业券商汇总统计历史表
		updateRealUtil.executeUpdateMethodOneParam(peerSecCountHistoryService::updatePeerSecCountHistory, 3);
		log.info("updatePeerSecCountHistory方法执行完毕，当前时间：{}", LocalDateTime.now());

		System.gc();

		log.info("此轮任务结束时间：{}", LocalDateTime.now());
	}


}

