package com.tjsj.modify.modules.margin.business.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.common.enums.MrgTrdDataType;
import com.tjsj.modify.modules.margin.business.mapper.ExchangeRuleThresholdSecMapper;
import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdConfigDO;
import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdSecDO;
import com.tjsj.modify.modules.margin.business.service.ExchangeRuleThresholdConfigService;
import com.tjsj.modify.modules.margin.business.service.ExchangeRuleThresholdSecService;
import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecDO;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ExchangeRuleThresholdSecServiceImpl
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/02/21
 * @description 交易所规则阈值证券服务实现类
 */
@Service
@Slf4j
public class ExchangeRuleThresholdSecServiceImpl
        extends ServiceImpl<ExchangeRuleThresholdSecMapper, ExchangeRuleThresholdSecDO>
        implements ExchangeRuleThresholdSecService {
    @Resource
    private ExchangeStaticPeSecService exchangeStaticPeSecService;
    @Resource
    private ExchangeRuleThresholdConfigService exchangeRuleThresholdConfigService;

    @Override
    public int insertOrUpdate(ExchangeRuleThresholdSecDO record) {
        return baseMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ExchangeRuleThresholdSecDO record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoUpdateExchangeRuleThresholdSecData() {

        // 获取折算率上限
        Set<ExchangeRuleThresholdSecDO> secExchangeRuleThresholdSet = this.getSecHaircutUpperLimit();

        // 获取保证金比例上限
        this.getMrgRatioUpperLimit(secExchangeRuleThresholdSet);

        // 获取旧数据
        List<ExchangeRuleThresholdSecDO> oldDataSet = this.list();

        // 分别构建旧列表和新列表的 key -> record 映射，避免重复计算唯一 key
        Map<String, ExchangeRuleThresholdSecDO> oldKeyMap = oldDataSet.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(), record.getHaircutUpperLimit(),
                                record.getHaircutUpperLimitWithoutStaticPe(),
                                record.getFinanceMarginRatioLowerLimit(), record.getShortMarginRatioLowerLimit()
                        ),
                        Function.identity()
                ));
        Map<String, ExchangeRuleThresholdSecDO> newKeyMap = secExchangeRuleThresholdSet.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(), record.getHaircutUpperLimit(),
                                record.getHaircutUpperLimitWithoutStaticPe(),
                                record.getFinanceMarginRatioLowerLimit(), record.getShortMarginRatioLowerLimit()
                        ),
                        Function.identity()
                ));

        // 删除旧列表中不存在于新列表中的记录
        List<Integer> idsToRemove = oldKeyMap.entrySet().stream()
                .filter(entry -> !newKeyMap.containsKey(entry.getKey()))
                .map(entry -> entry.getValue().getId())
                .collect(Collectors.toList());


        if (!idsToRemove.isEmpty()) {
            log.warn("更新交易所外规阈值对应的证券具体值 发现 {} 条无效记录，将删除", idsToRemove.size());
            this.removeByIds(idsToRemove);
        } else {
//            log.warn("更新交易所外规阈值对应的证券具体值 无无效记录");
        }


        // 插入新列表中不存在于旧列表中的记录
        List<ExchangeRuleThresholdSecDO> recordsToInsert = newKeyMap.entrySet().stream()
                .filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        if (!recordsToInsert.isEmpty()) {
            log.warn("更新交易所外规阈值对应的证券具体值 发现 {} 条新增记录，将插入", recordsToInsert.size());
            this.baseMapper.insertBatchSomeColumn(recordsToInsert);
        } else {
//            log.warn("更新交易所外规阈值对应的证券具体值 无新增记录");
        }

    }

    /**
     * 获取保证金比例上限
     *
     * @param secExchangeRuleThresholdSet 证券交易所规则门槛设置
     * <AUTHOR> Ye
     * @date 2025/03/04
     */
    private void getMrgRatioUpperLimit(Set<ExchangeRuleThresholdSecDO> secExchangeRuleThresholdSet) {

        // #3) 获取保证金比例上限
        List<ExchangeRuleThresholdConfigDO> exchangeRuleThresholdConfigList = exchangeRuleThresholdConfigService.list(
                Wrappers.<ExchangeRuleThresholdConfigDO>lambdaQuery()
                        .select(ExchangeRuleThresholdConfigDO::getTypeName,
                                ExchangeRuleThresholdConfigDO::getThresholdValue)
                        .eq(ExchangeRuleThresholdConfigDO::getMrdTrdType, MrgTrdDataType.UNDERLYING));
        Optional<ExchangeRuleThresholdConfigDO> financeMrgRatioLowerLimit = exchangeRuleThresholdConfigList.stream()
                .filter(config -> "融资保证金比例".equals(config.getTypeName()))
                .findFirst();

        // 融资保证金比例上限默认值为0.8，如果没有配置则使用默认值
        BigDecimal financeMrgRatioLowerLimitValue = financeMrgRatioLowerLimit.isPresent() ?
                financeMrgRatioLowerLimit.get()
                .getThresholdValue() : new BigDecimal("0.8");

        // 融券保证金比例上限默认值为1.0，如果没有配置则使用默认值
        Optional<ExchangeRuleThresholdConfigDO> secMrgRatioLowerLimit = exchangeRuleThresholdConfigList.stream()
                .filter(config -> "融券保证金比例".equals(config.getTypeName()))
                .findFirst();
        BigDecimal secMrgRatioLowerLimitValue = secMrgRatioLowerLimit.isPresent() ? secMrgRatioLowerLimit.get()
                .getThresholdValue() : new BigDecimal("1.0");
        secExchangeRuleThresholdSet.forEach(sec -> sec.setFinanceMarginRatioLowerLimit(financeMrgRatioLowerLimitValue)
                .setShortMarginRatioLowerLimit(secMrgRatioLowerLimitValue));

    }

    private Set<ExchangeRuleThresholdSecDO> getSecHaircutUpperLimit() {

        // #1) 股票类型的折算率上限
        Set<ExchangeRuleThresholdSecDO> stockHaircutUpperLimitSet = this.baseMapper.selectStockHaircutUpperLimit();

        // #1.1) 股票类型中，静态PE限制的证券折算率上限为0
        Set<String> staticPeLimitSecCodeSet =
                exchangeStaticPeSecService.list(Wrappers.<ExchangeStaticPeSecDO>lambdaQuery()
                        .select(ExchangeStaticPeSecDO::getSecCode))
                .stream()
                .map(ExchangeStaticPeSecDO::getSecCode)
                .collect(Collectors.toSet());
        stockHaircutUpperLimitSet.stream()
                .filter(sec -> staticPeLimitSecCodeSet.contains(sec.getSecCode()))
                .forEach(sec -> sec.setHaircutUpperLimit(BigDecimal.ZERO));


        // #2) 除股票类型的证券的折算率上限
        Set<ExchangeRuleThresholdSecDO> otherSecHaircutUpperLimitSet =
                this.baseMapper.selectOtherSecHaircutUpperLimit();
        stockHaircutUpperLimitSet.addAll(otherSecHaircutUpperLimitSet);
        return stockHaircutUpperLimitSet;

    }
}
