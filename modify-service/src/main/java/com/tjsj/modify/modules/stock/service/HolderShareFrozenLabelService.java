package com.tjsj.modify.modules.stock.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.stock.model.HolderShareFrozenLabelDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/12 16:55
 * @description
 */

public interface HolderShareFrozenLabelService extends IService<HolderShareFrozenLabelDO> {


    /**
     * 插入批量一些列
     *
     * @param savingList 保存列表
     * <AUTHOR>
     * @date 2024/12/12
     */
    void insertBatchSomeColumn(List<HolderShareFrozenLabelDO> savingList);

    /**
     * 更新股东股份冻结标签表
     *
     * <AUTHOR>
     * @date 2025/02/09
     */
    void updateHolderShareFrozenLabelTable();
}
