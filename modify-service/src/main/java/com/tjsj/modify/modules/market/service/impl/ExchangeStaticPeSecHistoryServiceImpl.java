package com.tjsj.modify.modules.market.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.market.mapper.ExchangeStaticPeSecHistoryMapper;
import com.tjsj.modify.modules.market.mapper.MarketUpdateUtilMapper;
import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecHistoryDO;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecHistoryService;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/29 11:44
 * @description
 */

@Service
public class ExchangeStaticPeSecHistoryServiceImpl extends ServiceImpl<ExchangeStaticPeSecHistoryMapper,
        ExchangeStaticPeSecHistoryDO> implements ExchangeStaticPeSecHistoryService {
    @Resource
    private ExchangeStaticPeSecService exchangeStaticPeSecService;

    @Resource
    private MarketUpdateUtilMapper marketUpdateUtilMapper;

    @Override
    public void autoUpdateExchangeStaticPeSecDataHistory() {


        boolean ifTodayLastTradingDayThisWeekAndMarketDataUpdated =
                marketUpdateUtilMapper.checkIfLastTradingDayThisWeekAndMarketDataUpdated() > 5000;

        if (!ifTodayLastTradingDayThisWeekAndMarketDataUpdated) {
            return;
        }

        List<ExchangeStaticPeSecHistoryDO> newStaticPeSecList =
                exchangeStaticPeSecService.list(Wrappers.lambdaQuery())
                        .stream()
                        .map(sec -> BeanUtil.copyProperties(sec, ExchangeStaticPeSecHistoryDO.class)
                                .setDate(LocalDate.now()))
                        .toList();

        List<ExchangeStaticPeSecHistoryDO> oldStaticPeSecList =
                this.list(Wrappers.<ExchangeStaticPeSecHistoryDO>lambdaQuery()
                        .select(ExchangeStaticPeSecHistoryDO::getSecCode, ExchangeStaticPeSecHistoryDO::getStaticPe,
                                ExchangeStaticPeSecHistoryDO::getDate)
                        .eq(ExchangeStaticPeSecHistoryDO::getDate, LocalDate.now()));


        // 先删除不存在的记录
        Map<String, ExchangeStaticPeSecHistoryDO> oldKeyMap = oldStaticPeSecList.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(), record.getStaticPe(), record.getDate()
                        ),
                        Function.identity()
                ));
        Map<String, ExchangeStaticPeSecHistoryDO> newKeyMap = newStaticPeSecList.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(), record.getStaticPe(), record.getDate()
                        ),
                        Function.identity()
                ));

        List<String> noExistStaticPeSecCodes = oldKeyMap.entrySet().stream()
                .filter(entry -> !newKeyMap.containsKey(entry.getKey()))
                .map(entry -> entry.getValue().getSecCode())
                .toList();
        if (CollUtil.isNotEmpty(noExistStaticPeSecCodes)) {
//            log.warn("需要删除的记录数量：{}", noExistStaticPeSecCodes.size());
            this.remove(Wrappers.<ExchangeStaticPeSecHistoryDO>lambdaQuery()
                    .in(ExchangeStaticPeSecHistoryDO::getSecCode, noExistStaticPeSecCodes));
        }

        // 再插入新增的记录
        List<ExchangeStaticPeSecHistoryDO> insertStaticPeSecList = newKeyMap.entrySet().stream()
                .filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
//        log.warn("需要插入的记录数量：{}", insertStaticPeSecList.size());
        if (CollUtil.isNotEmpty(insertStaticPeSecList)) {
            this.baseMapper.insertBatchSomeColumn(insertStaticPeSecList);
        }


    }

    @Override
    public void autoUpdateExchangeStaticPeSecDataHistoryNew() {

        List<String> historyDateList = this.baseMapper.getHistoryDate();
        // 过滤日期大于最近两个星期
        historyDateList = historyDateList.stream()
                .filter(date -> LocalDate.parse(date).isAfter(LocalDate.now().minusWeeks(2)))
                .toList();

        for (String date : historyDateList) {

            this.baseMapper.insertExchangeStaticPeDateData(date);
        }

    }


}
