package com.tjsj.modify.modules.peer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.peer.model.entity.SecPeerInfoDO;

/**
 * SecPeerInfoService
 *
 * <AUTHOR> Ye
 * @date 2024/8/9 10:35
 * @version 1.0.0
 * @description 同业券商-最新两融业务数据服务接口
 */
public interface SecPeerInfoService extends IService<SecPeerInfoDO> {


    /**
     * 更新证券同业信息表
     *
     *
     *
     * <AUTHOR>
     * @date 2025/06/06
     */
    void updateSecPeerInfoTable();

    /**
     * 将同业券商担保品表 折算率及是否担保品信息 更新到t_sec_peer_info表中
     *
     * @param enName en姓名
     * @param cnName cn名称
     *
     * <AUTHOR> Ye
     * @date 2025/06/06
     */
    void updateSecPeerInfoCollateral(String enName, String cnName);


}
