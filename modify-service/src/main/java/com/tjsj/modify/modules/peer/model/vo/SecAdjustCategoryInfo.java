package com.tjsj.modify.modules.peer.model.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.annotation.JSONField;
import com.tjsj.common.annotation.calculate.CalculateField;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

/**
 * SecAdjustCategoryInfo
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/19
 * @description 同业券商-证券集中度分组调整信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@FieldNameConstants
@Schema(description = "同业券商-证券集中度分组调整信息")
public class SecAdjustCategoryInfo extends SecAdjustBaseModel {

    /**
     * 评级调高数量
     */
    @JSONField(ordinal = 10)
    @CalculateField(value = PeerConsts.CATEGORY_UP)
    @Schema(description = "评级调高数量")
    @ExcelProperty(value = "评级调高数量", order = 100)
    private Integer levelUpCount = 0;

    /**
     * 评级调低数量
     */
    @JSONField(ordinal = 20)
    @CalculateField(value = PeerConsts.CATEGORY_DOWN)
    @Schema(description = "评级调低数量")
    @ExcelProperty(value = "评级调低数量", order = 200)
    private Integer levelDownCount = 0;

    /**
     * 评级调出数量
     */
    @JSONField(ordinal = 30)
    @CalculateField(value = PeerConsts.CATEGORY_OUT)
    @Schema(description = "评级调出数量")
    @ExcelProperty(value = "评级调出数量", order = 300)
    private Integer levelOutCount = 0;


}
