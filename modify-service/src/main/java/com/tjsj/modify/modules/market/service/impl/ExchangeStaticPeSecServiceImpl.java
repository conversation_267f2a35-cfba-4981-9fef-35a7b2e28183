package com.tjsj.modify.modules.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.market.mapper.ExchangeStaticPeSecMapper;
import com.tjsj.modify.modules.market.mapper.MarketUpdateUtilMapper;
import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecDO;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecService;
import com.tjsj.modules.base.service.TradingDayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/1/20 9:51
 * @description
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class ExchangeStaticPeSecServiceImpl extends ServiceImpl<ExchangeStaticPeSecMapper, ExchangeStaticPeSecDO>
        implements ExchangeStaticPeSecService {
    @Resource
    private TradingDayService tradingDayService;

    private final MarketUpdateUtilMapper marketUpdateUtilMapper;


    @Override
    public void autoUpdateExchangeStaticPeSecData() {

        // 判断当日是否是本周最后一个交易日，且行情表已经更新完成
        boolean ifTodayLastTradingDayThisWeekAndMarketDataUpdated =
                marketUpdateUtilMapper.checkIfLastTradingDayThisWeekAndMarketDataUpdated() > 5000;

        List<ExchangeStaticPeSecDO> oldStaticPeSecList = this.list(Wrappers.<ExchangeStaticPeSecDO>lambdaQuery()
                .select(ExchangeStaticPeSecDO::getSecCode, ExchangeStaticPeSecDO::getStaticPe,
                        ExchangeStaticPeSecDO::getDate));

        List<ExchangeStaticPeSecDO> newStaticPeSecList = marketUpdateUtilMapper.selectNewExchangeStaticPeSecData(
                ifTodayLastTradingDayThisWeekAndMarketDataUpdated
        );

        // 先删除不存在的记录
        Map<String, ExchangeStaticPeSecDO> oldKeyMap = oldStaticPeSecList.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(), record.getStaticPe(), record.getDate()
                        ),
                        Function.identity()
                ));
        Map<String, ExchangeStaticPeSecDO> newKeyMap = newStaticPeSecList.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(), record.getStaticPe(), record.getDate()
                        ),
                        Function.identity()
                ));

        List<String> noExistStaticPeSecCodes = oldKeyMap.entrySet().stream()
                .filter(entry -> !newKeyMap.containsKey(entry.getKey()))
                .map(entry -> entry.getValue().getSecCode())
                .toList();
        if (CollUtil.isNotEmpty(noExistStaticPeSecCodes)) {
//            log.warn("需要删除的记录数量：{}", noExistStaticPeSecCodes.size());
            this.remove(Wrappers.<ExchangeStaticPeSecDO>lambdaQuery()
                    .in(ExchangeStaticPeSecDO::getSecCode, noExistStaticPeSecCodes));
        }

        // 再插入新增的记录
        List<ExchangeStaticPeSecDO> insertStaticPeSecList = newKeyMap.entrySet().stream()
                .filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
//        log.warn("需要插入的记录数量：{}", insertStaticPeSecList.size());
        if (CollUtil.isNotEmpty(insertStaticPeSecList)) {
            this.baseMapper.insertBatchSomeColumn(insertStaticPeSecList);
        }

    }

}
