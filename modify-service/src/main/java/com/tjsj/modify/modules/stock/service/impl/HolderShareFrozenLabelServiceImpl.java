package com.tjsj.modify.modules.stock.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.stock.mapper.HolderShareFrozenLabelMapper;
import com.tjsj.modify.modules.stock.mapper.StockUpdateUtilMapper;
import com.tjsj.modify.modules.stock.model.HolderShareFrozenLabelDO;
import com.tjsj.modify.modules.stock.service.HolderShareFrozenLabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/12 16:55
 * @description
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class HolderShareFrozenLabelServiceImpl extends ServiceImpl<HolderShareFrozenLabelMapper,
		HolderShareFrozenLabelDO> implements HolderShareFrozenLabelService {

	private final StockUpdateUtilMapper stockUpdateUtilMapper;

	@Override
	public void insertBatchSomeColumn(List<HolderShareFrozenLabelDO> savingList) {
		this.baseMapper.insertBatchSomeColumn(savingList);
	}

	@Override
	public void updateHolderShareFrozenLabelTable() {

		List<String> holderTypeList = Arrays.asList("控股股东", "大股东");
		holderTypeList.forEach(holderType -> {
			List<HolderShareFrozenLabelDO> holderShareFrozenInfoList =
					stockUpdateUtilMapper.listHolderFrozenInfo(holderType);

			holderShareFrozenInfoList = this.processHolderShareFrozenInfoList(holderShareFrozenInfoList);
			List<HolderShareFrozenLabelDO> holderShareFrozenLabelInfoList = holderShareFrozenInfoList.parallelStream()
					.flatMap(holderShareFrozenInfo -> {
						String secCode = holderShareFrozenInfo.getSecCode();
						String frozenPerson = holderShareFrozenInfo.getFrozenPerson();
						Long frozenNumber = holderShareFrozenInfo.getFrozenNumber();
						String infoDescribe = holderShareFrozenInfo.getInfoDescribe();
						LocalDate date = holderShareFrozenInfo.getDate();
						List<HolderShareFrozenLabelDO> afterProcessLabelInfoList =
								stockUpdateUtilMapper.getSecFrozenPersonLabelInfo(secCode, frozenPerson, frozenNumber,
										holderType);
						afterProcessLabelInfoList.forEach(tHolderShareFrozenLabelDO ->
								tHolderShareFrozenLabelDO.setInfoDescribe(infoDescribe).setDate(date));
						return afterProcessLabelInfoList.stream();
					})
					.collect(Collectors.toList());

			// 过滤掉没有标签的记录
			holderShareFrozenLabelInfoList = holderShareFrozenLabelInfoList.stream()
					.filter(holderShareFrozenLabelDO -> holderShareFrozenLabelDO.getLabelName() != null)
					.collect(Collectors.toList());


			this.updateHolderShareFrozenLabelTable(holderType, holderShareFrozenLabelInfoList);

		});
	}

	private void updateHolderShareFrozenLabelTable(String holderType,
	                                               List<HolderShareFrozenLabelDO> newLabelList) {

		List<HolderShareFrozenLabelDO> oldLabelList = this.list(Wrappers.<HolderShareFrozenLabelDO>lambdaQuery()
				.select(HolderShareFrozenLabelDO::getId, HolderShareFrozenLabelDO::getSecCode,
						HolderShareFrozenLabelDO::getDate, HolderShareFrozenLabelDO::getLabelName,
						HolderShareFrozenLabelDO::getFrozenPerson, HolderShareFrozenLabelDO::getInfoDescribe)
				.like(HolderShareFrozenLabelDO::getLabelName, holderType));

		// 分别构建旧列表和新列表的 key -> record 映射，避免重复计算唯一 key
		oldLabelList = ModifyDataUtil.distinctByList(oldLabelList, HolderShareFrozenLabelDO::getSecCode,
				HolderShareFrozenLabelDO::getDate, HolderShareFrozenLabelDO::getLabelName,
				HolderShareFrozenLabelDO::getFrozenPerson, HolderShareFrozenLabelDO::getInfoDescribe);

		Map<String, HolderShareFrozenLabelDO> oldKeyMap = oldLabelList.stream()
				.collect(Collectors.toMap(
						record -> ModifyDataUtil.buildUniqueKey(
								record.getSecCode(),
								record.getDate(),
								record.getLabelName(),
								record.getFrozenPerson(),
								record.getInfoDescribe()
						),
						Function.identity()
				));


		newLabelList = ModifyDataUtil.distinctByList(newLabelList, HolderShareFrozenLabelDO::getSecCode,
				HolderShareFrozenLabelDO::getDate,
				HolderShareFrozenLabelDO::getLabelName, HolderShareFrozenLabelDO::getFrozenPerson,
				HolderShareFrozenLabelDO::getInfoDescribe);
		Map<String, HolderShareFrozenLabelDO> newKeyMap = newLabelList.stream()
				.collect(Collectors.toMap(
						record -> ModifyDataUtil.buildUniqueKey(
								record.getSecCode(),
								record.getDate(),
								record.getLabelName(),
								record.getFrozenPerson(),
								record.getInfoDescribe()
						),
						Function.identity()
				));

		// 删除旧列表中不存在于新列表中的记录
		List<Integer> idsToRemove = oldKeyMap.entrySet().stream()
				.filter(entry -> !newKeyMap.containsKey(entry.getKey()))
				.map(entry -> entry.getValue().getId())
				.collect(Collectors.toList());

		if (!idsToRemove.isEmpty()) {
			log.warn("updateHolderShareFrozenLabelTable 发现 {} 条无效记录，将删除", idsToRemove.size());
			this.removeByIds(idsToRemove);
		} else {
//			log.info("updateHolderShareFrozenLabelTable 无无效记录");
		}


		// 插入新列表中不存在于旧列表中的记录
		List<HolderShareFrozenLabelDO> recordsToInsert = newKeyMap.entrySet().stream()
				.filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
				.map(Map.Entry::getValue)
				.collect(Collectors.toList());

		if (!recordsToInsert.isEmpty()) {
			log.warn("updateHolderShareFrozenLabelTable 发现 {} 条新增记录，将插入", recordsToInsert.size());
			this.baseMapper.insertBatchSomeColumn(recordsToInsert);
		} else {
//			log.info("updateHolderShareFrozenLabelTable 无新增记录");
		}

	}

	/**
	 * 处理股东股份冻结信息列表
	 * <p>对相似股东名称进行合并，并计算冻结数量</p>
	 *
	 * @param holderShareFrozenInfoList 股东股份冻结信息列表
	 * @return {@link List }<{@link HolderShareFrozenLabelDO }>
	 * <AUTHOR> Ye
	 * @date 2024/12/12
	 */
	private List<HolderShareFrozenLabelDO> processHolderShareFrozenInfoList(List<HolderShareFrozenLabelDO> holderShareFrozenInfoList) {
		// 按照证券代码对控股股东冻结信息分组并排序
		Map<String, List<HolderShareFrozenLabelDO>> secHolderShareFrozenInfoMap = holderShareFrozenInfoList.stream()
				.collect(Collectors.groupingBy(
						HolderShareFrozenLabelDO::getSecCode,
						TreeMap::new,
						Collectors.toList()
				));

		List<HolderShareFrozenLabelDO> afterProcessHolderShareFrozenList = new ArrayList<>();

		// 每个证券的控股股东冻结情况
		for (Map.Entry<String, List<HolderShareFrozenLabelDO>> secCodeEntry : secHolderShareFrozenInfoMap.entrySet()) {
			List<HolderShareFrozenLabelDO> secHolderShareFrozenList = secCodeEntry.getValue();
			Map<String, Long> mergedPersonFrozenNumberMap = MapUtil.newHashMap();
			for (HolderShareFrozenLabelDO secHolderShareFrozenInfo : secHolderShareFrozenList) {
				String person = secHolderShareFrozenInfo.getFrozenPerson();
				Long frozenNumber = secHolderShareFrozenInfo.getFrozenNumber();

				// 判断是否存在具有相似字符的合并目标
				boolean ifMergeFrozenPerson = false;
				for (Map.Entry<String, Long> mergedEntry : mergedPersonFrozenNumberMap.entrySet()) {
					String mergingPerson = mergedEntry.getKey();
					if (isSimilar(person, mergingPerson)) {
						// 合并到已存在的目标中
						Long mergedFrozenNumber = mergedEntry.getValue();
						mergedEntry.setValue(mergedFrozenNumber + frozenNumber);
						ifMergeFrozenPerson = true;
						break;
					}
				}
				if (!ifMergeFrozenPerson) {
					mergedPersonFrozenNumberMap.put(person, frozenNumber);
				}
			}
			Set<String> frozenPersonSet = mergedPersonFrozenNumberMap.keySet();
			secHolderShareFrozenList = secHolderShareFrozenList.stream()
					.filter(secHolderShareFrozen -> frozenPersonSet.contains(secHolderShareFrozen.getFrozenPerson()))
					.toList();
			for (HolderShareFrozenLabelDO secHolderShareFrozen : secHolderShareFrozenList) {
				String frozenPerson = secHolderShareFrozen.getFrozenPerson();
				if (mergedPersonFrozenNumberMap.containsKey(frozenPerson)) {
					secHolderShareFrozen.setFrozenNumber(mergedPersonFrozenNumberMap.get(frozenPerson));
				}
			}
			afterProcessHolderShareFrozenList.addAll(secHolderShareFrozenList);
		}
		return afterProcessHolderShareFrozenList;
	}

	private static boolean isSimilar(String str1, String str2) {
		return str1.contains(str2) || str2.contains(str1);
	}


}
