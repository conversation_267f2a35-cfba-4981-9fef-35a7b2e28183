package com.tjsj.modify.modules.stock.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.annotation.ReviewDate;
import com.tjsj.common.utils.data.HashUtil;
import com.tjsj.modify.modules.stock.mapper.HolderShareFrozenMapper;
import com.tjsj.modify.modules.stock.mapper.StockUpdateUtilMapper;
import com.tjsj.modify.modules.stock.model.HolderShareFrozenDO;
import com.tjsj.modify.modules.stock.service.HolderShareFrozenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/12 13:54
 * @description
 */

@Service
@RequiredArgsConstructor
@Slf4j
public class HolderShareFrozenServiceImpl extends ServiceImpl<HolderShareFrozenMapper, HolderShareFrozenDO>
        implements HolderShareFrozenService {

    private final StockUpdateUtilMapper stockUpdateUtilMapper;

    /**
     * 更新 pledgedata.t_holder_share_frozen 表
     *
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    @Override
    @CheckCount(count = 1)
    @ReviewDate(reviewDates = {"2025-02-09"})
    public void updateHolderShareFrozenTable() {

        // 循环删除重复数据
        loopDeleteFrozenTableDuplicateData();

        List<HolderShareFrozenDO> oldHolderShareFrozenList = this.list();
        List<HolderShareFrozenDO> newHolderShareFrozenList = stockUpdateUtilMapper.selectNewHolderShareFrozenList();

        // 判断旧列表和新列表的hash值是否相同，如果相同则无需更新
        // 为了正确比较，需要基于清理后的数据计算newHash
        String oldHash = HashUtil.generateHash(oldHolderShareFrozenList);

        // 创建临时清理后的数据用于hash计算，不修改原始newHolderShareFrozenList
        List<HolderShareFrozenDO> cleanedNewDataForHash = newHolderShareFrozenList.stream()
                .peek(record -> {
                    // 创建一个临时的字符串表示，用于hash计算
                    String frozenPerson = record.getFrozenPerson();
                    String cleanedFrozenPerson = (frozenPerson != null && !frozenPerson.isEmpty())
                            ? frozenPerson.replaceAll("\\d+$", "") : frozenPerson;

                    BeanUtil.copyProperties(record, HolderShareFrozenDO.class).setFrozenPerson(cleanedFrozenPerson);
                })
                .collect(Collectors.toList());

        String newHash = HashUtil.generateHash(cleanedNewDataForHash);
        if (Objects.equals(oldHash, newHash)) {
            log.info("updateHolderShareFrozenTable 无需更新");
            return;
        }

        oldHolderShareFrozenList.stream().filter(oldRecord -> oldRecord.getHash() == null)
                .forEach(oldRecord -> {
                    String oldRecordHash = HashUtil.generateHash(oldRecord);
                    oldRecord.setHash(oldRecordHash);
                });
        newHolderShareFrozenList.forEach(newRecord -> {
            String newRecordHash = HashUtil.generateHash(newRecord);
            newRecord.setHash(newRecordHash);
        });


        // 分别构建旧列表和新列表的 key -> record 映射，避免重复计算唯一 key
        Map<String, HolderShareFrozenDO> oldKeyMap = oldHolderShareFrozenList.stream()
                .collect(Collectors.toMap(HolderShareFrozenDO::getHash, Function.identity()));
        Map<String, HolderShareFrozenDO> newKeyMap = newHolderShareFrozenList.stream()
                .collect(Collectors.toMap(HolderShareFrozenDO::getHash, Function.identity()));

        // 删除旧列表中不存在于新列表中的记录
        List<Integer> idsToRemove = oldKeyMap.entrySet().stream()
                .filter(entry -> !newKeyMap.containsKey(entry.getKey()))
                .map(entry -> entry.getValue().getId())
                .collect(Collectors.toList());

        if (!idsToRemove.isEmpty()) {
            log.info("updateHolderShareFrozenTable 发现 {} 条无效记录，将删除", idsToRemove.size());
            this.removeByIds(idsToRemove);
        } else {
            log.info("updateHolderShareFrozenTable 无无效记录");
        }

        // 插入新列表中不存在于旧列表中的记录
        List<HolderShareFrozenDO> recordsToInsert = newKeyMap.entrySet().stream()
                .filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

        if (!recordsToInsert.isEmpty()) {
            log.warn("updateHolderShareFrozenTable 发现 {} 条新增记录，将插入", recordsToInsert.size());
            // 在插入前清理frozenPerson字段的末尾数字
            recordsToInsert.forEach(record -> {
                String frozenPerson = record.getFrozenPerson();
                if (frozenPerson != null && !frozenPerson.isEmpty()) {
                    // 使用正则表达式去除末尾的数字
                    String cleanedFrozenPerson = frozenPerson.replaceAll("\\d+$", "");
                    record.setFrozenPerson(cleanedFrozenPerson);
                }
            });
            this.baseMapper.insertBatchSomeColumn(recordsToInsert);
        } else {
            log.warn("updateHolderShareFrozenTable 无新增记录");
        }

    }

    /**
     * 循环删除pledgedata.t_frozen表中的重复数据
     *
     * <AUTHOR> Ye
     * @date 2025/07/17
     */
    private void loopDeleteFrozenTableDuplicateData() {

        // 每轮循环删除一批数据，直到没有重复数据为止

        while (true){
            int deleteCount = stockUpdateUtilMapper.deleteFrozenTableDuplicateData();
            if (deleteCount == 0){
                break;
            }
        }

    }

}
