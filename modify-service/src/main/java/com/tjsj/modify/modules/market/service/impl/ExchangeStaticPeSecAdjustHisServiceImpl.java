package com.tjsj.modify.modules.market.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modify.modules.market.mapper.ExchangeStaticPeSecAdjustHisMapper;
import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecAdjustHisDO;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecAdjustHisService;
import com.tjsj.modify.modules.stock.service.StockInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ExchangeStaticPeSecAdjustHisServiceImpl
 *
 * <AUTHOR>
 * @date 2025/06/11
 * @version 1.0.0
 * @description 交易所静态市盈率证券调整历史服务实现类
 */
@Service
public class ExchangeStaticPeSecAdjustHisServiceImpl
        extends ServiceImpl<ExchangeStaticPeSecAdjustHisMapper, ExchangeStaticPeSecAdjustHisDO>
        implements ExchangeStaticPeSecAdjustHisService {

    @Resource
    private StockInfoService stockInfoService;

    @Override
    public void autoUpdateExchangeStaticPeSecAdjustHisData() {

        List<String> historyDateList = this.baseMapper.getHistoryDateList();

        for (int index = 0; index < historyDateList.size(); index++) {

            if (index == historyDateList.size() - 1) {
                return;
            }

            String afterDate = historyDateList.get(index);
            String beforeDate = historyDateList.get(index + 1);

            this.baseMapper.insertStaticPeSecAdjustHis(beforeDate, afterDate);
        }


    }


}
