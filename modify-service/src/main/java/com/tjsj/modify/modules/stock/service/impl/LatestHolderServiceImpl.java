package com.tjsj.modify.modules.stock.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.annotation.ReviewDate;
import com.tjsj.common.utils.data.ModifyDataUtil;
import com.tjsj.modify.modules.stock.mapper.LatestHolderMapper;
import com.tjsj.modify.modules.stock.mapper.StockUpdateUtilMapper;
import com.tjsj.modify.modules.stock.model.LatestHolderInfoDO;
import com.tjsj.modify.modules.stock.service.HolderShareFrozenService;
import com.tjsj.modify.modules.stock.service.LatestHolderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * LatestHolderServiceImpl
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2024/09/08
 * @description 最新股东服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LatestHolderServiceImpl extends ServiceImpl<LatestHolderMapper, LatestHolderInfoDO>
        implements LatestHolderService {
    @Resource
    private HolderShareFrozenService holderShareFrozenService;

    private final StockUpdateUtilMapper stockUpdateUtilMapper;

    @Override
    @CheckCount(count = 1)
    @ReviewDate(reviewDates = {"2025-02-08 21:08"})
    public void autoUpdateLatestHolderData() {

        String oldFilterSecCode = "";
        String newFilterSecCode = "";
        List<LatestHolderInfoDO> oldLatestHolderList = this.getOldLatestHolderList(oldFilterSecCode);
        List<LatestHolderInfoDO> newLatestHolderList = this.getNewLatestHolderList(newFilterSecCode);

        // 计算用时时间
        long startTime = System.currentTimeMillis();

        // 分别构建旧列表和新列表的 key -> record 映射，避免重复计算唯一 key
        Map<String, LatestHolderInfoDO> oldKeyMap = oldLatestHolderList.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(),
                                record.getSecName(),
                                record.getHolderName(),
                                record.getDate(),
                                record.getHoldingNumber(),
                                record.getPledgeRatio(),
                                record.getHolderOrder(),
                                record.getShareholdingRatio(),
                                record.getRefId()
                        ),
                        Function.identity()
                ));
        Map<String, LatestHolderInfoDO> newKeyMap = newLatestHolderList.stream()
                .collect(Collectors.toMap(
                        record -> ModifyDataUtil.buildUniqueKey(
                                record.getSecCode(),
                                record.getSecName(),
                                record.getHolderName(),
                                record.getDate(),
                                record.getHoldingNumber(),
                                record.getPledgeRatio(),
                                record.getHolderOrder(),
                                record.getShareholdingRatio(),
                                record.getRefId()
                        ),
                        Function.identity()
                ));

        // 删除旧列表中不存在于新列表中的记录
        List<Long> idsToRemove = oldKeyMap.entrySet().stream()
                .filter(entry -> !newKeyMap.containsKey(entry.getKey()))
                .map(entry -> entry.getValue().getId())
                .collect(Collectors.toList());


        if (!idsToRemove.isEmpty()) {
            log.warn("autoUpdateLatestHolderData 发现 {} 条无效记录，将删除", idsToRemove.size());
            this.removeByIds(idsToRemove);
        } else {
//            log.warn("autoUpdateLatestHolderData 无无效记录");
        }


        // 插入新列表中不存在于旧列表中的记录
        List<LatestHolderInfoDO> recordsToInsert = newKeyMap.entrySet().stream()
                .filter(entry -> !oldKeyMap.containsKey(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        if (!recordsToInsert.isEmpty()) {
            log.warn("autoUpdateLatestHolderData 发现 {} 条新增记录，将插入", recordsToInsert.size());
            this.baseMapper.insertBatchSomeColumn(recordsToInsert);
        } else {
//            log.warn("autoUpdateLatestHolderData 无新增记录");
        }


        long endTime = System.currentTimeMillis();
//        log.warn("autoUpdateLatestHolderData 耗时：{} ms", endTime - startTime);
    }

    /**
     * 获取老最新股东列表
     *
     * @param oldFilterSecCode 老过滤证券代码
     * @return {@link List }<{@link LatestHolderInfoDO }>
     * <AUTHOR> Ye
     * @date 2025/02/09
     */
    private List<LatestHolderInfoDO> getOldLatestHolderList(String oldFilterSecCode) {
        return this.list(Wrappers.<LatestHolderInfoDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(oldFilterSecCode), LatestHolderInfoDO::getSecCode, oldFilterSecCode));
    }

    /**
     * 获取新最新股东列表
     *
     * @param newFilterSecCode 新过滤证券代码
     * @return {@link List }<{@link LatestHolderInfoDO }>
     * <AUTHOR> Ye
     * @date 2025/02/08
     */
    private List<LatestHolderInfoDO> getNewLatestHolderList(String newFilterSecCode) {
        /**
         * [背景] 本方法实现上市公司十大股东数据更新逻辑：
         * - 现行策略：基于最新财务报告期提取各证券的最新股东数据
         * - 数据源特征：同时包含定期财务报告（季报/半年报/年报）和临时公告两类数据源
         *
         * [问题分析] 临时公告数据存在披露规范不一致问题：
         * 1. 数据完整性差异：
         *    - 部分临时公告（如股东权益变动公告）未包含股东质押数据
         *    - 定期报告通常包含完整质押信息
         * 示例：
         *    - 证券000034：2024-06-30半年报（含质押） vs 2024-06-27临时公告（无质押）
         *    - 证券000048：2024-06-30定期报告（含质押） vs 2024-06-20临时公告（无质押）
         *    - 证券002377：2024-09-30三季报与2024-10-21临时公告均含质押
         *
         * 2. 核心矛盾：
         *    - 临时公告披露内容缺乏强制规范，质押信息披露具有非确定性
         *    - 导致合并数据集的字段一致性无法保证
         */

        List<LatestHolderInfoDO> newLatestHolderList =
                stockUpdateUtilMapper.selectNewLatestHolderList(newFilterSecCode);
        List<String> newSecCodeList =
                newLatestHolderList.stream().map(LatestHolderInfoDO::getSecCode).toList();
        // 再获取财务报告期没有十大股东数据，也就是最近新上市的股票的十大股东数据
        List<LatestHolderInfoDO> newLatestHolderList2 =
                stockUpdateUtilMapper.selectNewLatestHolderList2(newSecCodeList, newFilterSecCode);
        newLatestHolderList.addAll(newLatestHolderList2);

        return newLatestHolderList;
    }


    @Override
    public void distinctHolderHistoryTable() {
        //  根据px股东属性和number持股数量进行去重
        stockUpdateUtilMapper.distinctHolderHistoryTable();
    }




}
