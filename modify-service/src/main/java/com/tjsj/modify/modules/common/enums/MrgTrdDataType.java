package com.tjsj.modify.modules.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import com.tjsj.modify.modules.peer.model.vo.SecAdjustBaseModel;
import com.tjsj.modify.modules.peer.model.vo.SecAdjustCategoryInfo;
import com.tjsj.modify.modules.peer.model.vo.SecAdjustCollateralInfo;
import com.tjsj.modify.modules.peer.model.vo.SecAdjustUnderlyingInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * PeerDataType
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/8/11 20:04
 * @description 两融业务类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "PeerDataType", description = "两融业务类型枚举")
public enum MrgTrdDataType implements BaseEnum {

    /**
     * 担保品
     */
    COLLATERAL("collateral", "担保品", Arrays.asList(PeerConsts.COLLATERAL_OUT, PeerConsts.COLLATERAL_HAIRCUT_INCREASE,
            PeerConsts.COLLATERAL_HAIRCUT_REDUCE, PeerConsts.COLLATERAL_IN), SecAdjustCollateralInfo.class),

    /**
     * 标的证券
     */
    UNDERLYING("underlying", "标的证券", Arrays.asList(PeerConsts.MARGIN_SECURITY_OUT, PeerConsts.SHORT_SECURITY_OUT,
            PeerConsts.FINANCE_MARGIN_RATIO_INCREASE, PeerConsts.FINANCE_MARGIN_RATIO_REDUCE,
            PeerConsts.SHORT_MARGIN_RATIO_INCREASE, PeerConsts.SHORT_MARGIN_RATIO_REDUCE),
            SecAdjustUnderlyingInfo.class),

    /**
     * 证券分类
     */
    CATEGORY("category", "集中度分组", Arrays.asList(PeerConsts.CATEGORY_UP, PeerConsts.CATEGORY_DOWN,
            PeerConsts.CATEGORY_OUT), SecAdjustCategoryInfo.class);

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    /**
     * 存储的调整类型
     */
    private List<String> storedAdjustTypes;

    /**
     * 调整类
     */
    private Class<? extends SecAdjustBaseModel> adjustClass;


    @Override
    public Object getTransferValue() {
        return code;
    }

}
