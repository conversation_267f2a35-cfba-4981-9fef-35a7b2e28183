package com.tjsj.modify.modules.margin.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.common.enums.MrgTrdDataType;
import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdConfigDO;

import java.util.List;

/**
 * ExchangeRuleThresholdConfigService
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/02/19
 * @description 交易所规则阈值配置服务
 */
public interface ExchangeRuleThresholdConfigService extends IService<ExchangeRuleThresholdConfigDO> {


    int insertOrUpdate(ExchangeRuleThresholdConfigDO record);

    int insertOrUpdateSelective(ExchangeRuleThresholdConfigDO record);

    /**
     * 获取两融业务外规阈值配置
     *
     * @param dataType 数据类型
     * @return {@link List }<{@link ExchangeRuleThresholdConfigDO }>
     * <AUTHOR> Ye
     * @date 2025/02/19
     */
    List<ExchangeRuleThresholdConfigDO> getMarginRuleThresholdConfig(MrgTrdDataType dataType);

    /**
     * 修改两融业务外规规则阈值配置
     *
     * @param dataType                     数据类型
     * @param exchangeRuleThresholdConfigs 交易所规则阈值配置
     * <AUTHOR> Ye
     * @date 2025/02/19
     */
    void modifyMarginRuleThresholdConfig(MrgTrdDataType dataType,
                                         List<ExchangeRuleThresholdConfigDO> exchangeRuleThresholdConfigs);
}
