package com.tjsj.modify.modules.margin.business.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * ExchangeRuleThresholdSecDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/02/21
 * @description 交易所外规对于业务参数的阈值对应的证券具体值
 */
@Schema(description = "交易所外规对于业务参数的阈值对应的证券具体值")
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@Alias(value = "ExchangeRuleThresholdSecDO")
@TableName(value = "margin.t_exchange_rule_threshold_sec")
public class ExchangeRuleThresholdSecDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 证券代码带后缀
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码带后缀")
    @JSONField(ordinal = 100)
    private String secCode;

    /**
     * 折算率上限值
     */
    @TableField(value = "haircut_upper_limit")
    @Schema(description = "折算率上限值")
    @JSONField(ordinal = 200)
    private BigDecimal haircutUpperLimit;

    /**
     * 折算率上限值不考虑静态市盈率
     */
    @TableField(value = "haircut_upper_limit_without_static_pe")
    @Schema(description = "折算率上限值不考虑静态市盈率")
    @JSONField(ordinal = 210)
    private BigDecimal haircutUpperLimitWithoutStaticPe;

    /**
     * 融资保证金比例下限值
     */
    @TableField(value = "finance_margin_ratio_lower_limit")
    @Schema(description = "融资保证金比例下限值")
    @JSONField(ordinal = 300)
    private BigDecimal financeMarginRatioLowerLimit;

    /**
     * 融券保证金比例下限值
     */
    @TableField(value = "short_margin_ratio_lower_limit")
    @Schema(description = "融券保证金比例下限值")
    @JSONField(ordinal = 400)
    private BigDecimal shortMarginRatioLowerLimit;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}