package com.tjsj.modify.modules.market.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecHistoryDO;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/29 11:44
 * @description
 */

public interface ExchangeStaticPeSecHistoryService extends IService<ExchangeStaticPeSecHistoryDO> {


	/**
	 * 自动更新交易所静态市盈率外规历史数据
	 *
	 * <AUTHOR>
	 * @date 2025/05/29
	 */
	void autoUpdateExchangeStaticPeSecDataHistory();


	/**
	 * 自动更新交易所静态市盈率外规历史数据
	 *
	 * <AUTHOR>
	 * @date 2025/05/29
	 */
	void autoUpdateExchangeStaticPeSecDataHistoryNew();

}
