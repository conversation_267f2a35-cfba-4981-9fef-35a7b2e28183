package com.tjsj.modify.modules.peer.mapper;


import com.tjsj.modify.modules.common.enums.MrgTrdDataType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * PeerTableMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/02/03
 * @description 同业券商融资融券表Mapper
 */
@Mapper
public interface PeerTableMapper {
    /**
     * 统计最大日期Market字段未检查记录
     *
     * @param tableName      表名称
     * @param mrgTrdDataType 融资融券数据类型
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2025/02/03
     */
    Integer countMaxDateMarketNonCheckRecord(@Param("tableName") String tableName,
                                             @Param("mrgTrdDataType") MrgTrdDataType mrgTrdDataType);
}
