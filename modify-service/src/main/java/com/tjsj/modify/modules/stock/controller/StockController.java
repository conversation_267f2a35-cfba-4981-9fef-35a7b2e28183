package com.tjsj.modify.modules.stock.controller;

import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.env.ProfileEnvSetting;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.common.enums.basic.ProfileTypeEnum;
import com.tjsj.common.utils.data.UpdateRealUtil;
import com.tjsj.modify.modules.stock.service.LatestHolderService;
import com.tjsj.modify.modules.stock.service.StockUpdateUtilService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * StockController
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/2/8 16:19
 * @description 股票数据
 */
@Tag(name = "StockController", description = "股票数据")
@RestController
@RequestMapping("/stock")
@RequiredArgsConstructor
@Slf4j
@Validated
public class StockController {

    private final UpdateRealUtil updateRealUtil;

    private final LatestHolderService latestHolderService;

    private final StockUpdateUtilService stockUpdateUtilService;

    @Operation(summary = "自动更新上市公司股东相关数据")
    @GetMapping("/autoUpdateCompanyShareholderData")
    @PassToken
    @Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟更新一次
    @ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
    @SingleInstanceLock
    public void autoUpdateCompanyShareholderData() {

        // 去重十大股东历史表 pledgedata.t_tenshareholderinfos
        updateRealUtil.executeUpdateMethod(latestHolderService::distinctHolderHistoryTable,
                "distinctHolderHistoryTable");

        // 自动更新最新十大股东表 pledgedata.t_latest_holder_info
        updateRealUtil.executeUpdateMethod(latestHolderService::autoUpdateLatestHolderData,
                "autoUpdateLatestHolderData");

        // 自动更新股东股份冻结相关表 tj_middle_ground.t_holder_share_frozen labels. t_holder_share_frozen_label
        updateRealUtil.executeUpdateMethod(stockUpdateUtilService::autoUpdateHolderShareFrozenTable,
                "autoUpdateHolderShareFrozenTable");

        // 去重股东减持公告表 credit.t_dfcf_equity_pledge
        updateRealUtil.executeUpdateMethod(stockUpdateUtilService::distinctHolderReductionTable,
                "distinctHolderReductionTable");
    }

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Operation(summary = "自动更新上市公司重大事项相关数据")
    @GetMapping("/autoUpdateCompanyMaterialEventData")
    @PassToken
    @Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟更新一次
    @ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
    @SingleInstanceLock
    public void autoUpdateCompanyMaterialEventData() {

        // 去重违规信息表 pledgedata.t_criminalrecords
        updateRealUtil.executeUpdateMethod(stockUpdateUtilService::distinctCriminalRecordTable,
                "distinctCriminalRecordTable");
    }

    @Operation(summary = "自动更新股票市场行情相关数据")
    @GetMapping("/autoUpdateStockMarketData")
    @PassToken
    @Scheduled(fixedDelay = 1000 * 60 * 10) // 每10分钟更新一次
    @ProfileEnvSetting(allowProfiles = {ProfileTypeEnum.PROD})
    @SingleInstanceLock
    public void autoUpdateStockMarketData() {

        // 更新全市场单一股票质押比例
        updateRealUtil.executeUpdateMethod(stockUpdateUtilService::updateStockMarketPledgeRatio,
                "updateStockMarketPledgeRatio");
        // 更新全市场单一股票担保物比例
        updateRealUtil.executeUpdateMethod(stockUpdateUtilService::updateStockMarketCollateralRatio,
                "updateStockMarketCollateralRatio");

    }


}
