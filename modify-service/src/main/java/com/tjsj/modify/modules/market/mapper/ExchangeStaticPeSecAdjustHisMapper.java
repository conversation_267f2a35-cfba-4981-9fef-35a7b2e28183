package com.tjsj.modify.modules.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modify.modules.market.model.entity.ExchangeStaticPeSecAdjustHisDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ExchangeStaticPeSecAdjustHisMapper
 *
 * <AUTHOR>
 * @date 2025/06/11
 * @version 1.0.0
 * @description 交易所静态市盈率规则折算率调整历史记录表-Mapper接口
 */
@Mapper
public interface ExchangeStaticPeSecAdjustHisMapper extends BaseMapper<ExchangeStaticPeSecAdjustHisDO> {


    /**
     * 获取历史日期列表
     *
     *
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2025/06/11
     */
    List<String> getHistoryDateList();


    /**
     * 获取指定日期的股票静态市盈率
     *
     * @param date 日期
     * @return {@link List }<{@link ExchangeStaticPeSecAdjustHisDO }>
     * <AUTHOR> Ye
     * @date 2025/06/11
     */
    List<ExchangeStaticPeSecAdjustHisDO> getStockStaticPeByDate(@Param("date") String date);


    /**
     * 插入静态市盈率证券调整历史
     *
     * @param beforeDate 日期之前
     * @param afterDate 日期之后
     *
     * <AUTHOR> Ye
     * @date 2025/06/11
     */
    void insertStaticPeSecAdjustHis(@Param("beforeDate") String beforeDate, @Param("afterDate") String afterDate);

}