package com.tjsj.modify.modules.stock.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modify.modules.stock.model.LatestHolderInfoDO;

/**
 * LatestHolderService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/09/08
 * @description 最新股东服务
 */
public interface LatestHolderService extends IService<LatestHolderInfoDO> {
    /**
     * 自动更新最新股东数据
     *
     * <AUTHOR>
     * @date 2025/02/08
     */
    void autoUpdateLatestHolderData();

    /**
     * 去重股东历史表 pledgedata.t_tenshareholderinfos
     *
     * <AUTHOR>
     * @date 2025/02/08
     */
    void distinctHolderHistoryTable();


}
