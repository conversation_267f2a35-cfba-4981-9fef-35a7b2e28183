package com.tjsj.modify.modules.peer.mapper;

import com.tjsj.modify.modules.peer.model.dto.PeerDataFixDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * PeerDataMapper
 *
 * <AUTHOR>
 * @date 2024/07/19
 * @description 同业数据映射器
 */
@Mapper
@Schema(description = "同业数据映射器")
public interface PeerDataMapper {


    /**
     * 获取同业表市场领域非数据
     *
     * @param peerTableName 表名称
     * @param schemaName    架构名称
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2025/01/01
     */
    List<PeerDataFixDTO> getPeerTableMarketFieldNullValueData(@Param("peerTableName") String peerTableName,
                                                              @Param("schemaName") String schemaName);

    /**
     * 更新市场字段
     *
     * @param tableName       表名称
     * @param schemaName      架构名称
     * @param bestMatchRecord 最佳匹配记录
     * @param maxDateTime     最大日期时间
     * <AUTHOR> Ye
     * @date 2025/01/01
     */
    void updateMarketField(@Param("tableName") String tableName,
                           @Param("schemaName") String schemaName,
                           @Param("bestMatchRecord") PeerDataFixDTO bestMatchRecord,
                           @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 获取同业表市场领域非数据通过基金方法
     *
     * @param tableName  同业名称
     * @param schemaName 架构名称
     * @return {@link List }<{@link PeerDataFixDTO }>
     * <AUTHOR> Ye
     * @date 2025/01/01
     */
    List<PeerDataFixDTO> getPeerTableMarketFieldNonDataByFundMethod(@Param("tableName") String tableName,
                                                                    @Param("schemaName") String schemaName);

    /**
     * 获取同业表市场领域非选中通过基金方法
     *
     * @param tableName   表名称
     * @param schemaName  架构名称
     * @param maxDateTime
     * @return {@link List }<{@link PeerDataFixDTO }>
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    List<PeerDataFixDTO> getPeerTableMarketFieldNonCheckedByFundMethod(@Param("tableName") String tableName,
                                                                       @Param("schemaName") String schemaName,
                                                                       LocalDateTime maxDateTime);

    /**
     * 添加同业表enable_status字段
     *
     * @param enName         en姓名
     * @param tableSuffix    表后缀
     * @param tableFieldName 表字段名
     * <AUTHOR> Ye
     * @date 2025/01/01
     */
    void addPeerTableEnableStatusField(@Param("tableName") String enName, @Param("schemaName") String tableSuffix,
                                       @Param("tableFieldName") String tableFieldName);

    /**
     * 筛选出所有缺少指定字段的同业券商数据表
     *
     * @param schemaName     架构名称
     * @param tableFieldName 表enable_status字段
     * @return {@link Iterable }<{@link Object }>
     * <AUTHOR> Ye
     * @date 2025/01/01
     */
    List<String> selectPeerTableWithoutDesignateField(@Param("schemaName") String schemaName,
                                                      @Param("tableFieldName") String tableFieldName);

    /**
     * 选择同业表没有if_market_checked字段
     *
     * @param schemaName 架构名称
     * @return {@link Iterable }<{@link Object }>
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    List<String> selectPeerTableWithoutIfMarketCheckedField(@Param("schemaName") String schemaName);

    /**
     * 添加同业表`if_market_checked`字段
     *
     * @param tableName      表名称
     * @param schemaName     架构名称
     * @param tableFieldName 表字段名
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    void addPeerTableIfMarketCheckedField(@Param("tableName") String tableName,
                                          @Param("schemaName") String schemaName,
                                          @Param("tableFieldName") String tableFieldName);

    /**
     * 选择有效的同业券商的所有表名称列表
     *
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    List<String> selectValidPeerSecuritiesTableNameList();

    /**
     * 插入股票类型错误记录到错误表
     *
     * @param tableName                            表名称
     * @param maxDateTime                          最大日期时间
     * @param ifThisTableMarketFieldNullValueTable 当前表是否为market字段为null值表
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    void insertStockTypeWrongRecordToErrorTable(@Param("tableName") String tableName,
                                                @Param("maxDateTime") LocalDateTime maxDateTime,
                                                @Param("ifThisTableMarketFieldNullValueTable") Boolean ifThisTableMarketFieldNullValueTable);

    /**
     * 更新股票类型错误记录
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    void fixMarketFieldErrorValueByStockType(@Param("tableName") String tableName,
                                             @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 更新股票类型错误记录
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/06/13
     */
    void fixMarketFieldErrorValueByStockTypeNew(@Param("tableName") String tableName,
                                                @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 获取同业券商表market字段未检查过的记录数
     *
     * @param tableName 表名称
     * @return {@link Integer }
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    Integer getPeerTableMarketFieldNonCheckRecordCount(@Param("tableName") String tableName);

    /**
     * 更新匹配基金或债券类型市场领域
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/02/02
     */
    void updateMatchedFundOrBondTypeMarketField(@Param("tableName") String tableName,
                                                @Param("maxDateTime") LocalDateTime maxDateTime);



    /**
     * 更新匹配基金或债券类型市场现场2
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    void updateMatchedFundOrBondTypeMarketField2(@Param("tableName") String tableName,
                                                 @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 获取同业表Market字段未检查过的数据
     *
     * @param tableName   表名称
     * @param schemaName  架构名称
     * @param maxDateTime 最大日期时间
     * @return {@link List }<{@link PeerDataFixDTO }>
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    List<PeerDataFixDTO> getPeerTableMarketFieldNonCheckedData(@Param("tableName") String tableName,
                                                               @Param("schemaName") String schemaName,
                                                               @Param("maxDateTime") LocalDateTime maxDateTime);


    /**
     * 更新退市证券enable_status为disable
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    void updateDeListSecurityEnableStatusToDisable(@Param("tableName") String tableName,
                                                   @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 选择同业表最大值更新时间
     *
     * @param tableName 表名称
     * @return {@link LocalDateTime }
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    LocalDateTime selectPeerTableMaxUpdateTime(@Param("tableName") String tableName);

    /**
     * 更新未匹配证券的if_market_checked字段为2
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/02/03
     */
    void updateUnmatchedSecurityIfMarketCheckedTo2(@Param("tableName") String tableName,
                                                   @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 更新Market字段通过过去日期匹配记录
     *
     * @param tableName   表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/02/06
     */
    void updateMarketFieldByPastDateMatchedRecord(@Param("tableName") String tableName,
                                                  @Param("maxDateTime") LocalDateTime maxDateTime);

    /**
     * 插入或更新同业折算率数据
     *
     * @param enName    en姓名
     * @param tableName 表名称
     * @param date      日期
     * <AUTHOR> Ye
     * @date 2024/12/14
     */
    void insertOrUpdatePeerHaircutData(@Param("enName") String enName, @Param("tableName") String tableName,
                                       @Param("date") String date);

    /**
     * 更新margin.t_peer_sec_finance_target_data表中的同业融资标的数据
     *
     * @param enName    en姓名
     * @param tableName 表名称
     * @param date      日期
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    void insertOrUpdatePeerFinanceTargetData(@Param("enName") String enName, @Param("tableName") String tableName,
                                             @Param("date") String date);

    /**
     * 更新margin.t_peer_sec_short_sell_target_data表中的 同业融券标的 数据
     *
     * @param enName    en姓名
     * @param tableName 表名称
     * @param date      日期
     * <AUTHOR> Ye
     * @date 2024/12/14
     */
    void insertOrUpdatePeerShortSellTargetData(@Param("enName") String enName, @Param("tableName") String tableName,
                                               @Param("date") String date);

    /**
     * 插入或更新同业分类数据
     *
     * @param enName      en姓名
     * @param tableName   表名称
     * @param tradingDate 交易日期
     * <AUTHOR> Ye
     * @date 2024/12/16
     */
    void insertOrUpdatePeerCategoryData(@Param("enName") String enName, @Param("tableName") String tableName,
                                        @Param("tradingDate") String tradingDate);

    /**
     * 插入或更新同业分类数据非存在
     *
     * @param categoryNonExistPeerSecurities 分类非存在同业证券
     * @param date                           日期
     * <AUTHOR> Ye
     * @date 2024/12/17
     */
    void updatePeerCategoryDataNonExist(
            @Param("categoryNonExistPeerSecurities") List<String> categoryNonExistPeerSecurities,
            @Param("date") String date);

    /**
     * 更新同业平均折算率数据
     *
     * @param tradingDate 日期
     * <AUTHOR> Ye
     * @date 2024/12/16
     */
    void updatePeerAverageHaircutData(@Param("tradingDate") String tradingDate);

    /**
     * 更新同业平均融资标的数据
     *
     * @param tradingDate 交易日期
     * <AUTHOR> Ye
     * @date 2025/02/10
     */
    void updatePeerAverageFinanceTargetData(@Param("tradingDate") String tradingDate);

    /**
     * 更新同业平均融券标的数据
     *
     * @param tradingDate 交易日期
     * <AUTHOR> Ye
     * @date 2024/12/16
     */
    void updatePeerAverageShortSellTargetData(@Param("tradingDate") String tradingDate);


    /**
     * 更新同业表Market字段
     *
     * @param tableName 表名称
     *
     * <AUTHOR> Ye
     * @date 2024/12/16
     */
    void updateTableMarketField(@Param("tableName") String tableName);

    /**
     * 清空同业表Market字段临时表
     *
     * <AUTHOR> Ye
     * @date 2024/12/16
     */
    void truncatePeerMrgTrdMarketTempTable();

    /**
     * 插入或更新同业表Market字段临时表
     *
     * @param tableName 表名称
     * @param maxDateTime 最大日期时间
     * <AUTHOR> Ye
     * @date 2025/06/12
     */
    void updateMatchedFundOrBondTypeMarketFieldNew(@Param("tableName") String tableName,
                                                   @Param("maxDateTime") LocalDateTime maxDateTime);


}
