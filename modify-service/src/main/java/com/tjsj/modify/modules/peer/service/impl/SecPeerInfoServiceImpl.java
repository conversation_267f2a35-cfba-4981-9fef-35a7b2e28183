package com.tjsj.modify.modules.peer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modify.modules.peer.common.consts.PeerConsts;
import com.tjsj.modify.modules.peer.mapper.SecPeerInfoMapper;
import com.tjsj.modify.modules.peer.model.entity.PeerSecuritiesSetting;
import com.tjsj.modify.modules.peer.model.entity.SecPeerInfoDO;
import com.tjsj.modify.modules.peer.service.PeerSecuritiesSettingService;
import com.tjsj.modify.modules.peer.service.SecPeerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> Ye
 * @date 2024/8/9 10:35
 * @description
 */
@Service
@Slf4j
public class SecPeerInfoServiceImpl extends ServiceImpl<SecPeerInfoMapper, SecPeerInfoDO>
        implements SecPeerInfoService {

    @Resource
    private PeerSecuritiesSettingService peerSecuritiesSettingService;


    @Override
    public void updateSecPeerInfoTable() {

        List<PeerSecuritiesSetting> peerSecuritiesSettings = peerSecuritiesSettingService.queryPeerAgencyList(true)
                .stream()
                .sorted(Comparator.comparing(PeerSecuritiesSetting::getRanking))
                .toList();

        for (PeerSecuritiesSetting peerSecuritiesConfig : peerSecuritiesSettings) {

            String enName = peerSecuritiesConfig.getEnName();
            String cnName = peerSecuritiesConfig.getCnName();

            // 1.将同业券商担保品表 折算率及是否担保品信息 更新到t_sec_peer_info表中
            this.updateSecPeerInfoCollateral(enName, cnName);

        }

    }

    @Override
    public void updateSecPeerInfoCollateral(String enName, String cnName) {

        // 组建表名
        String peerCollateralTableName = PeerConsts.PEER_PREFIX + enName + PeerConsts.COLLATERAL_SUFFIX;
        this.baseMapper.updateSecPeerInfoCollateral(peerCollateralTableName, cnName);


    }


}
