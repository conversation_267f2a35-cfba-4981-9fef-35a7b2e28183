package com.tjsj.modify.modules.margin.business.mapper;

import com.tjsj.modify.modules.margin.business.model.entity.ExchangeRuleThresholdConfigDO;
import com.tjsj.modules.base.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * ExchangeRuleThresholdConfigMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/02/19
 * @description 交易所规则阈值配置映射器
 */
@Mapper
public interface ExchangeRuleThresholdConfigMapper extends IBaseMapper<ExchangeRuleThresholdConfigDO> {
    int insertOrUpdate(ExchangeRuleThresholdConfigDO record);

    int insertOrUpdateSelective(ExchangeRuleThresholdConfigDO record);
}