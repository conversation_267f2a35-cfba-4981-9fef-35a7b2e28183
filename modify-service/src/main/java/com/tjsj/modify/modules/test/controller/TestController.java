package com.tjsj.modify.modules.test.controller;

import com.tjsj.common.annotation.UpdateAnnotation;
import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.utils.data.UpdateRealUtil;
import com.tjsj.modify.modules.market.service.ExchangeStaticPeSecAdjustHisService;
import com.tjsj.modify.modules.peer.service.impl.PeerDataService;
import com.tjsj.modify.modules.stock.service.HolderShareFrozenService;
import com.tjsj.modify.modules.stock.service.StockUpdateUtilService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * TestController
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/2/10 17:51
 * @description 测试控制器
 */
@Tag(name = "TestController", description = "测试控制器")
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Slf4j
@Validated
@Profile("dev")
public class TestController {
    @Resource
    private PeerDataService peerDataService;

    private final UpdateRealUtil updateRealUtil;

    private final StockUpdateUtilService stockUpdateUtilService;


    @Operation(summary = "测试更新同业数据")
    @GetMapping("/testUpdatePeerSecData")
    @UpdateAnnotation
    public void testUpdatePeerSecData(@RequestParam("startDate") String startDate,
                                      @RequestParam("endDate") String endDate) {

        // 更新全市场单一股票担保物比例
        updateRealUtil.executeUpdateMethod(stockUpdateUtilService::updateStockMarketCollateralRatio,
                "updateStockMarketCollateralRatio");
    }

    private final ExchangeStaticPeSecAdjustHisService exchangeStaticPeSecAdjustHisService;

    private final HolderShareFrozenService holderShareFrozenService;

    @Operation(summary = "测试")
    @GetMapping("/test1")
    @PassToken
    public void test1() {


        peerDataService.fixPeerTableMarketFieldErrorRecord("t_rzrq_xnzq_zsl", List.of("t_rzrq_xnzq_zsl"));
    }

}