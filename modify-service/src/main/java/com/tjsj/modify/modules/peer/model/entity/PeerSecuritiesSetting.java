package com.tjsj.modify.modules.peer.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.basic.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * PeerSecuritiesSetting
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/07/19
 * @description 同业券商信息表
 */
@TableName(value = "credit.t_finance_settings")
@Data
@Accessors(chain = true)
@Schema(name = "PeerSecuritiesSetting对象", description = "同业券商信息表")
@Alias("PeerSecuritiesSetting")
public class PeerSecuritiesSetting implements Serializable {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 简称
     */
    @TableField(value = "en_name")
    private String enName;

    /**
     * 中文名称
     */
    @TableField(value = "cn_name")
    private String cnName;

    /**
     * 券商排名
     */
    @TableField(value = "ranking")
    private Integer ranking;

    /**
     * 集中度是否有全量
     */
    @TableField(value = "jzd_full")
    private Integer jzdFull;

    /**
     * 集中度分类
     */
    @TableField(value = "jzd_cls")
    private Integer jzdCls;

    /**
     * 集中度是否采集
     */
    @TableField(value = "jzd_cj")
    private Integer jzdCj;

    /**
     * 集中度是否有调整
     */
    @TableField(value = "jzd_tz")
    private Integer jzdTz;

    /**
     * 折算率是否全量
     */
    @TableField(value = "zsl_full")
    private Integer zslFull;

    /**
     * 备注
     */
    @TableField(value = "mark1")
    private String mark1;

    /**
     * 1:正常；2：维护中；3：开发中
     */
    @TableField(value = "mark2")
    private String mark2;

    /**
     *
     */
    @TableField(value = "url")
    private String url;

    /**
     * 集中度控制指标链接
     */
    @TableField(value = "jzd_url")
    private String jzdUrl;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 是否在同业统计表格中展示
     */
    @TableField(value = "if_column_show")
    private CommonStatusEnum ifColumnShow;

    /**
     * 是否有担保状态
     */
    @TableField(value = "db_status")
    private Integer dbStatus;

    /**
     * 需要更新market字段的表
     */
    @TableField(value = "market_field_update_table")
    private String marketFieldUpdateTable;

    /**
     * 是否启用：0启用，1不启用
     */
    @Schema(description = "是否启用：0启用，1不启用")
    @TableField(value = "enable_status")
    private CommonStatusEnum enableStatus;

    /**
     *
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}